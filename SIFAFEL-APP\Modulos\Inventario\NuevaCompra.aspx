<%@ Page Title="Nueva Compra" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="NuevaCompra.aspx.cs" Inherits="SIFAFEL_APP.Modulos.Inventario.NuevaCompra" %>

<%@ Register Src="~/WebUserControls/WUC_InfoProduct.ascx" TagPrefix="uc1" TagName="WUC_InfoProduct" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">
    <uc1:WUC_InfoProduct runat="server" ID="WUC_InfoProduct" />

    <%-- MODAL PROVEEDORES --%>
    <div class="modal fade effect-scale" id="modalProveedores">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h4 class="modal-title"><i data-feather="truck" class="feather-16"></i>&nbsp;Consultas de proveedores</h4>
                    <button type="button" aria-label="Close" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-start">

                    <table id="grvProveedores" class="table table-bordered table-striped table-condensed table-sm">
                        <thead class="table-head">
                            <tr>
                                <th>Código</th>
                                <th>NIT</th>
                                <th>Proveedor</th>
                                <th>Teléfono</th>
                                <th>Correo</th>
                                <th>Direccion</th>
                                <th>&nbsp;</th>
                            </tr>
                        </thead>
                    </table>

                </div>
                <div class="modal-footer d-flex justify-content-end">
                    <button type="button" class="btn btn-cancel btn-sm" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <%-- MODAL PRODUCTOS --%>
    <div class="modal fade effect-scale" id="modalProductos">
        <div class="modal-dialog modal-fullscreen" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h4 class="modal-title">Productos</h4>
                    <button type="button" aria-label="Close" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-start">

                    <table id="grvProductosModal" class="table table-bordered table-striped table-condensed table-sm">
                        <thead class="table-head">
                            <tr>
                                <th>Código</th>
                                <th>Producto</th>
                                <th>Precio</th>
                                <th>Stock</th>
                                <th>&nbsp;</th>
                            </tr>
                        </thead>
                    </table>

                </div>
                <div class="modal-footer d-flex justify-content-end">
                    <button type="button" class="btn btn-cancel btn-sm" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <%-- HEADER --%>
    <div class="page-header">
        <div class="add-item d-flex">
            <div class="page-title">
                <h4><i data-feather="shopping-bag" class="me-2"></i>Nueva compra</h4>
                <h6>Nueva compra</h6>
            </div>
        </div>
        <div class="page-btn">
            <a href="Compras.aspx" class="btn btn-added color">
                <i data-feather="inbox" class="me-2"></i>Listado de compras
            </a>
        </div>
    </div>

    <%-- COMPRAS --%>
    <div class="row compras">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">

                    <div class="row">
                        <!-- SECCION DE PROVEEDOR -->
                        <div class="col-md-8">
                            <div class="form-horizontal">

                                <!-- NIT PROVEEDOR -->
                                <div class="mb-1 row">
                                    <div class="add-newplus">
                                        <span id="lblIdProveedor" hidden=""></span>
                                        <label for="txtNitProveedor" class="form-label">Proveedor:</label>

                                        <a href="#!" id="btnBuscarProveedor">
                                            <i data-feather="search" class="plus-down-add"></i>
                                            <span>Buscar</span>
                                        </a>
                                    </div>
                                    <div class="col-12">
                                        <div class="row g-0">
                                            <div class="col-4 input-h-control">
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">
                                                        <i data-feather="truck" class="feather-16"></i>
                                                    </span>
                                                    <input id="txtNitProveedor" name="txtNitProveedor" type="text" aria-label="NIT" placeholder="NIT" class="form-control no-right-border">
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <input id="txtNombreProveedor" type="text" aria-label="Nombre completo" placeholder="Nombre completo" class="form-control no-left-border no-right-border input-h-control" disabled="disabled">
                                            </div>
                                            <div class="col-4">
                                                <div class="input-group input-group-sm">
                                                    <input id="txtFechaCompra" name="txtFechaCompra" type="date" aria-label="Fecha" class="form-control no-left-border input-h-control">
                                                    <span class="input-group-text">
                                                        <i data-feather="calendar" class="feather-16"></i>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- SEGUNDA FILA: CAMPOS ADICIONALES -->
                                <div class="mb-1 row">
                                    <div class="col-12">
                                        <div class="row g-0">
                                            <div class="col-4 input-h-control">
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">
                                                        <i data-feather="file-text" class="feather-16"></i>
                                                    </span>
                                                    <input id="txtCodigoCompra" name="txtCodigoCompra" type="text" aria-label="Código" placeholder="Código compra" class="form-control no-right-border">
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <input id="txtPolizaDuca" name="txtPolizaDuca" type="text" aria-label="Póliza/DUCA" placeholder="Póliza/DUCA" class="form-control no-left-border no-right-border input-h-control">
                                            </div>
                                            <div class="col-4">
                                                <select id="ddlFormaPago" name="ddlFormaPago" class="form-select no-left-border input-h-control">
                                                    <option value="">Forma de pago</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <!-- SECCION DE COMPRA -->
                        <div class="col-md-4">
                            <div class="form-horizontal">

                                <%-- BOTONES DE ACCIÓN --%>
                                <div class="mb-1 row">
                                    <div class="col-sm-12">
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-primary btn-sm" id="btnGuardarCompra">
                                                <i data-feather="save" class="feather-16"></i>&nbsp;Guardar Compra
                                            </button>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                        <span class="card-title" style="float: left; width: auto !important;">
                            <i data-feather="package" class="feather-16"></i>&nbsp;Productos
                        </span>

                        <%-- CODIGO DE BARRA --%>
                        <div class="col-lg-4 col-sm-12 ms-auto">
                            <div class="add-newplus">
                                <label class="form-label" for="txtCodigoBarraProducto">&nbsp;</label>
                                <a href="#!" id="btnBuscarProducto">
                                    <i data-feather="search" class="plus-down-add"></i>
                                    <span>Consultar</span>
                                </a>
                            </div>
                            <div class="input-blocks">
                                <div class="input-groupicon select-code">
                                    <input id="txtCodigoBarraProducto" name="txtCodigoBarraProducto" class="barcode-search" type="text" placeholder="Código de producto" style="padding: 10px;">
                                    <div class="addonset">
                                        <img src="<%=ConfigurationManager.AppSettings["url_cdn"]%>img/barcode-scanner.gif" alt="img" style="height: 38px;">
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <%-- PRODUCTOS COMPRA --%>
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="grvProductosCompra" class="table table-striped table-bordered table-sm">
                                <thead class="table-head">
                                    <tr>
                                        <th>Id</th>
                                        <th>Código</th>
                                        <th>Producto</th>
                                        <th>Cantidad</th>
                                        <th>Existencia</th>
                                        <th>Costo U.</th>
                                        <th>Costo Total</th>
                                        <th>Precio Venta</th>
                                        <th>Precio Mínimo</th>
                                        <th>&nbsp;</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>

    <style>
        /* Estilos para inputs sin bordes */
        .no-left-border {
            border-left: none !important;
            border-radius: 0 !important;
        }

        .no-right-border {
            border-right: none !important;
            border-radius: 0 !important;
        }

        .input-h-control {
            height: 38px;
        }

        /* Estilos para DataTable */
        #grvProductosCompra td {
            vertical-align: middle !important;
            padding: 8px 6px !important;
        }

        #grvProductosCompra .form-control {
            height: 28px !important;
            padding: 4px 8px !important;
            font-size: 12px !important;
            border: 1px solid #ced4da !important;
            border-radius: 4px !important;
        }

        #grvProductosCompra .btn-sm {
            padding: 4px 8px !important;
            font-size: 12px !important;
        }

        .productimgname img {
            width: 32px !important;
            height: 32px !important;
            object-fit: cover;
            border-radius: 4px;
        }

        .view-product {
            display: inline-block;
            vertical-align: middle;
        }

        .view-info-product {
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
            font-size: 13px;
            text-decoration: none;
            color: #495057;
        }

        .view-info-product:hover {
            color: #007bff;
            text-decoration: none;
        }
    </style>

    <script type="text/javascript">
        // Variables globales
        var tbl_productos_compra;
        var tbl_proveedores;
        var productosCompra = [];

        $(document).ready(function () {
            // Inicializar fecha actual
            $('#txtFechaCompra').val(new Date().toISOString().split('T')[0]);

            // Inicializar DataTable de productos
            inicializarDataTableProductos();

            // Inicializar DataTable de proveedores
            inicializarDataTableProveedores();

            // Inicializar DataTable de productos del modal
            inicializarDataTableProductosModal();

            // Cargar formas de pago
            cargarFormasPago();

            // Eventos
            $('#btnBuscarProveedor').on('click', function() {
                $('#modalProveedores').modal('show');
            });

            $('#btnBuscarProducto').on('click', function() {
                $('#modalProductos').modal('show');
            });

            $('#txtCodigoBarraProducto').on('keypress', function(e) {
                if (e.which == 13) { // Enter
                    buscarProductoPorCodigo();
                }
            });

            $('#btnGuardarCompra').on('click', function() {
                guardarCompra();
            });

            // Buscar proveedor por NIT al presionar Enter
            $('#txtNitProveedor').on('keypress', function(e) {
                if (e.which == 13) { // Enter
                    buscarProveedorPorNit();
                }
            });
        });

        // Función para inicializar DataTable de productos
        function inicializarDataTableProductos() {
            tbl_productos_compra = $('#grvProductosCompra').DataTable({
                responsive: true,
                processing: true,
                language: {
                    url: '<%=ConfigurationManager.AppSettings["url_cdn"] %>vendor/datatables/Spanish.json'
                },
                columnDefs: [
                    {
                        targets: [0],
                        visible: false
                    },
                    {
                        targets: [3, 5, 6, 7, 8], // Columnas editables
                        orderable: false
                    },
                    {
                        targets: [9], // Columna de acciones
                        orderable: false,
                        className: 'text-center'
                    }
                ],
                columns: [
                    { data: "id_producto" },
                    { data: "codigo" },
                    {
                        data: function (item) {
                            let img_producto = '';
                            if (!item.img_producto) {
                                img_producto = '<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png';
                            } else {
                                img_producto = item.img_producto;
                            }
                            return '<div class="view-product me-2">' +
                                        '<a href="#!">' +
                                            '<img src="' + img_producto + '" alt="" onerror="this.onerror=null;this.src=\'<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png\';">' +
                                        '</a>' +
                                    '</div>' +
                                    '<a href="#!" class="view-info-product">' + item.nombre + '</a>';
                        }
                    },
                    // Columna 3: Cantidad (input editable)
                    {
                        data: function (item) {
                            return '<input type="number" class="form-control cantidad-input" value="1" min="1" onkeyup="validarCantidadCosto(this)" />';
                        }
                    },
                    // Columna 4: Existencia
                    { data: "stock_actual" },
                    // Columna 5: Costo U. (input editable)
                    {
                        data: function (item) {
                            return '<input type="number" class="form-control costo-input" value="' + (item.costo_unitario || 0) + '" step="0.01" min="0" onkeyup="validarCantidadCosto(this)" />';
                        }
                    },
                    // Columna 6: Costo Total (calculado)
                    {
                        data: function (item) {
                            return '<span class="costo-total">' + (item.costo_unitario || 0) + '</span>';
                        }
                    },
                    // Columna 7: Precio Venta (input editable)
                    {
                        data: function (item) {
                            return '<input type="number" class="form-control precio-input" value="' + (item.precio_unitario || 0) + '" step="0.01" min="0" onkeyup="validarPrecios(this)" />';
                        }
                    },
                    // Columna 8: Precio mínimo (input editable)
                    {
                        data: function (item) {
                            return '<input type="number" class="form-control precio-min-input" value="' + (item.min_descuento || 0) + '" step="0.01" min="0" onkeyup="validarPrecios(this)" />';
                        }
                    },
                    // Columna 9: Acciones
                    {
                        data: function (item) {
                            return '<button class="btn btn-primary btn-sm" type="button" title="Agregar a compra" onclick="AgregarProductoCompra(\'' + item.codigo + '\')">' +
                                        '<span class="fa fa-plus"></span>' +
                                    '</button>';
                        }
                    }
                ],
                rowCallback: function (row, data) {
                    $(row).find(".view-info-product").on("click", function () {
                        console.log("Ver info producto:", data);
                    });

                    // Agregar eventos para calcular costo total automáticamente
                    $(row).find('.cantidad-input, .costo-input').on('input', function() {
                        var cantidad = parseFloat($(row).find('.cantidad-input').val()) || 0;
                        var costoUnitario = parseFloat($(row).find('.costo-input').val()) || 0;
                        var costoTotal = cantidad * costoUnitario;
                        $(row).find('.costo-total').text(costoTotal.toFixed(2));
                    });
                }
            });
        }

        // Función para inicializar DataTable de proveedores
        function inicializarDataTableProveedores() {
            tbl_proveedores = $('#grvProveedores').DataTable({
                responsive: true,
                processing: true,
                language: {
                    url: '<%=ConfigurationManager.AppSettings["url_cdn"] %>vendor/datatables/Spanish.json'
                },
                ajax: {
                    url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                    type: 'POST',
                    data: function(d) {
                        var fmAuth = new FormData();
                        fmAuth.append("mth", mtdEnc("get/proveedores"));
                        fmAuth.append("data", mtdEnc(JSON.stringify({})));
                        return fmAuth;
                    },
                    contentType: false,
                    processData: false,
                    dataSrc: function(json) {
                        return json.data || [];
                    }
                },
                columns: [
                    { data: "id_proveedor" },
                    { data: "nit" },
                    { data: "nombres" },
                    { data: "telefono" },
                    { data: "correo" },
                    { data: "direccion" },
                    {
                        data: function(item) {
                            return '<button class="btn btn-primary btn-sm" onclick="seleccionarProveedor(\'' + item.id_proveedor + '\', \'' + item.nit + '\', \'' + item.nombres + '\')">' +
                                        '<i class="fa fa-check"></i>' +
                                    '</button>';
                        }
                    }
                ]
            });
        }

        // Función para inicializar DataTable de productos del modal
        function inicializarDataTableProductosModal() {
            $('#grvProductosModal').DataTable({
                responsive: true,
                processing: true,
                language: {
                    url: '<%=ConfigurationManager.AppSettings["url_cdn"] %>vendor/datatables/Spanish.json'
                },
                ajax: {
                    url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                    type: 'POST',
                    data: function(d) {
                        var fmAuth = new FormData();
                        fmAuth.append("mth", mtdEnc("get/products"));
                        return fmAuth;
                    },
                    contentType: false,
                    processData: false,
                    dataSrc: function(json) {
                        return json.data || [];
                    }
                },
                columns: [
                    { data: "codigo" },
                    { data: "nombre" },
                    {
                        data: function(item) {
                            return 'Q ' + (item.precio_unitario || 0).toFixed(2);
                        }
                    },
                    { data: "stock_actual" },
                    {
                        data: function(item) {
                            return '<button class="btn btn-primary btn-sm" onclick="seleccionarProducto(\'' + item.codigo + '\', \'' + item.nombre + '\')">' +
                                        '<i class="fa fa-check"></i>' +
                                    '</button>';
                        }
                    }
                ]
            });
        }

        // Función para seleccionar producto del modal
        function seleccionarProducto(codigo, nombre) {
            $('#txtCodigoBarraProducto').val(codigo);
            $('#modalProductos').modal('hide');
            buscarProductoPorCodigo();
        }

        // Función para buscar proveedor por NIT
        function buscarProveedorPorNit() {
            var nit = $('#txtNitProveedor').val().trim();
            if (!nit) {
                Swal.fire('Campo requerido', 'Ingrese el NIT del proveedor.', 'warning');
                return;
            }

            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/proveedor/by/nit"));
            fmAuth.append("nit", mtdEnc(nit));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response.type === "success" && response.data) {
                        $('#lblIdProveedor').text(response.data.id_proveedor);
                        $('#txtNombreProveedor').val(response.data.nombres);
                    } else {
                        Swal.fire('Proveedor no encontrado', 'No se encontró un proveedor con el NIT especificado.', 'warning');
                        $('#txtNombreProveedor').val('');
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Ocurrió un error al buscar el proveedor.', 'error');
                }
            });
        }

        // Función para seleccionar proveedor del modal
        function seleccionarProveedor(id, nit, nombre) {
            $('#lblIdProveedor').text(id);
            $('#txtNitProveedor').val(nit);
            $('#txtNombreProveedor').val(nombre);
            $('#modalProveedores').modal('hide');
        }

        // Función para cargar formas de pago
        function cargarFormasPago() {
            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/medio/pago/sucursal"));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response.type === "success" && response.data) {
                        $('#ddlFormaPago').empty().append('<option value="">Forma de pago</option>');
                        response.data.forEach(function(item) {
                            $('#ddlFormaPago').append('<option value="' + item.id_medio_pago + '">' + item.descripcion + '</option>');
                        });
                    }
                },
                error: function() {
                    console.log('Error al cargar formas de pago');
                }
            });
        }

        // Función para buscar producto por código
        function buscarProductoPorCodigo() {
            var codigo = $('#txtCodigoBarraProducto').val().trim();
            if (!codigo) {
                Swal.fire('Campo requerido', 'Ingrese el código del producto.', 'warning');
                return;
            }

            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/producto"));
            fmAuth.append("codigo", mtdEnc(codigo));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response.type === "success" && response.data && response.data.length > 0) {
                        var producto = response.data[0];
                        tbl_productos_compra.row.add(producto).draw();
                        $('#txtCodigoBarraProducto').val('');
                    } else {
                        Swal.fire('Producto no encontrado', 'No se encontró el producto con el código especificado.', 'warning');
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Ocurrió un error al buscar el producto.', 'error');
                }
            });
        }

        // Función para validar precios (basada en NuevoProducto.aspx)
        function validarPrecios(input) {
            var $row = $(input).closest('tr');
            var costoUnitario = parseFloat($row.find('.costo-input').val()) || 0;
            var precio = parseFloat($(input).val()) || 0;

            if (precio > 0 && costoUnitario > 0 && precio < costoUnitario) {
                Swal.fire({
                    icon: 'warning',
                    text: 'El precio no debe ser menor al costo del producto.',
                });
                $(input).val(costoUnitario);
            }
        }

        // Función para validar cantidad y costo
        function validarCantidadCosto(input) {
            var $row = $(input).closest('tr');
            var cantidad = parseFloat($row.find('.cantidad-input').val()) || 0;
            var costoUnitario = parseFloat($row.find('.costo-input').val()) || 0;
            var costoTotal = cantidad * costoUnitario;
            $row.find('.costo-total').text(costoTotal.toFixed(2));
        }

        // Función para agregar producto a la compra
        function AgregarProductoCompra(codigo) {
            // Aquí puedes agregar la lógica para agregar el producto a la compra
            console.log('Agregando producto:', codigo);
        }

        // Función para guardar compra
        function guardarCompra() {
            // Validar campos requeridos
            if (!$('#txtNitProveedor').val()) {
                Swal.fire('Campo requerido', 'Seleccione un proveedor.', 'warning');
                return;
            }

            if (!$('#txtFechaCompra').val()) {
                Swal.fire('Campo requerido', 'Ingrese la fecha de compra.', 'warning');
                return;
            }

            // Aquí puedes agregar la lógica para guardar la compra
            Swal.fire('Éxito', 'Compra guardada correctamente.', 'success');
        }
    </script>

</asp:Content>

﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="NuevaCompra.aspx.cs" Inherits="SIFAFEL_APP.Modulos.Inventario.NuevaCompra" %>

<%@ Register Src="~/WebUserControls/WUC_InfoProduct.ascx" TagPrefix="uc1" TagName="WUC_InfoProduct" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">
    <uc1:WUC_InfoProduct runat="server" ID="WUC_InfoProduct" />

    <!-- MODAL PARA BUSCAR PRODUCTOS -->
    <div class="modal fade" id="mdlBuscarProducto" tabindex="-1" aria-labelledby="mdlBuscarProductoLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mdlBuscarProductoLabel">Buscar Producto</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="input-group">
                                <input type="text" class="form-control" id="txtBuscarProductoModal" placeholder="Buscar producto por código o nombre" />
                                <button class="btn btn-primary" type="button" id="btnBuscarProductoModal">
                                    <i data-feather="search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table id="grvBuscarProducto" class="table table-striped table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Código</th>
                                            <th>Producto</th>
                                            <th>Precio</th>
                                            <th>Stock</th>
                                            <th>Acción</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Se llenará dinámicamente -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- MODAL PARA NUEVO PROVEEDOR -->
    <div class="modal fade" id="mdlNuevoProveedor" tabindex="-1" aria-labelledby="mdlNuevoProveedorLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mdlNuevoProveedorLabel">Nuevo Proveedor</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="txtNitNuevoProveedor" class="form-label">NIT:</label>
                            <input type="text" class="form-control" id="txtNitNuevoProveedor" placeholder="NIT del proveedor" />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="txtNombreNuevoProveedor" class="form-label">Nombre:</label>
                            <input type="text" class="form-control" id="txtNombreNuevoProveedor" placeholder="Nombre del proveedor" />
                        </div>
                    </div>
<%--                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="txtDireccionNuevoProveedor" class="form-label">Dirección:</label>
                            <input type="text" class="form-control" id="txtDireccionNuevoProveedor" placeholder="Dirección del proveedor" />
                        </div>
                    </div>--%>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="txtTelefonoNuevoProveedor" class="form-label">Teléfono:</label>
                            <input type="text" class="form-control" id="txtTelefonoNuevoProveedor" placeholder="Teléfono del proveedor" />
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="btnGuardarNuevoProveedor">Guardar</button>
                </div>
            </div>
        </div>
    </div>

    <%-- HEADER --%>
    <div class="page-header">
        <div class="add-item d-flex">
            <div class="page-title">
                <h4><i data-feather="shopping-bag" class="me-2"></i>Nueva compra</h4>
                <h6>Nueva compra</h6>
            </div>
        </div>
        <div class="page-btn">
            <a href="Compras.aspx" class="btn btn-added color">
                <i data-feather="inbox" class="me-2"></i>Listado de compras
            </a>
        </div>
    </div>

    <%-- CONTENIDO PRINCIPAL --%>
    <div class="row compras">
        <div class="col-md-12">
            <!-- SECCIÓN DE INFORMACIÓN DEL PROVEEDOR Y COMPRA -->
            <div class="row mb-3">
                <!-- TARJETA INFO PROVEEDOR -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i data-feather="user" class="me-2"></i>Info. proveedor</h6>
                        </div>
                        <div class="card-body">
                            <input type="hidden" id="idProveedor" name="idProveedor" />

                            <!-- NIT PROVEEDOR -->
                            <div class="mb-2">
                                <label for="nitProveedor" class="form-label">NIT:</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="nitProveedor" name="nitProveedor" placeholder="NIT del proveedor" />
                                    <button class="btn btn-primary" type="button" id="btnBuscarProveedor">
                                        <i data-feather="search"></i>
                                    </button>
                                    <button class="btn btn-success" type="button" id="btnBuscarProveedores" onclick="ListaProveedores()">
                                        <i data-feather="users"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- NOMBRE PROVEEDOR -->
                            <div class="mb-2">
                                <label for="nombreProveedor" class="form-label">Nombre:</label>
                                <input type="text" class="form-control" id="nombreProveedor" name="nombreProveedor" placeholder="Nombre del proveedor" disabled />
                            </div>

                            <!-- DIRECCION PROVEEDOR -->
                            <div class="mb-0">
                                <label for="direccionProveedor" class="form-label">Dirección:</label>
                                <input type="text" class="form-control" id="direccionProveedor" name="direccionProveedor" placeholder="Dirección de la empresa" disabled />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- TARJETA COMPRA -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i data-feather="shopping-cart" class="me-2"></i>Compra</h6>
                        </div>
                        <div class="card-body">
                            <!-- CÓDIGO DE COMPRA -->
                            <div class="mb-1">
                                <label for="codigoCompra" class="form-label">Código de compra:</label>
                                <input type="text" class="form-control" id="codigoCompra" name="codigoCompra" placeholder="No. Compra" />
                            </div>

                            <!-- PÓLIZA/DUCA -->
                            <div class="mb-1">
                                <label for="polizaDuca" class="form-label">Póliza/DUCA:</label>
                                <input type="text" class="form-control" id="polizaDuca" name="polizaDuca" placeholder="Número de póliza o DUCA" />
                            </div>

                            <!-- FECHA DE COMPRA -->
                            <div class="mb-1">
                                <label for="fechaCompra" class="form-label">Fecha Compra:</label>
                                <input type="date" class="form-control" id="fechaCompra" name="fechaCompra" />
                            </div>

                            <!-- FORMA DE PAGO -->
                            <div class="mb-0">
                                <label for="ddlFormaPago" class="form-label">Forma de pago:</label>
                                <select id="ddlFormaPago" name="ddlFormaPago" class="form-select">
                                    <option value="">Seleccionar forma de pago</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SECCIÓN DE PRODUCTOS -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <!-- BUSCADOR DE PRODUCTOS -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="codigoProducto" placeholder="Código de producto" />
                                        <button class="btn btn-primary" type="button" id="btnBuscarProducto">
                                            <i data-feather="search"></i>
                                        </button>
                                        <button class="btn btn-success" type="button" id="btnCargarProductos">
                                            <i data-feather="package" class="me-1"></i>Cargar Productos
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- TABLA DE PRODUCTOS -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="table-responsive">
                                        <table id="grvProductosCompra" class="table">
                                            <thead class="">
                                                <tr>
                                                    <th>Id Producto</th>
                                                    <th><i class="fa fa-barcode"></i>&nbsp;Código</th>
                                                    <th><i class="fa fa-box"></i>&nbsp;Producto</th>
                                                    <th>Cantidad</th>
                                                    <th>Existencia</th>
                                                    <th>Costo U.</th>
                                                    <th>Costo Total</th>
                                                    <th>Precio Venta</th>
                                                    <th>Precio mínimo</th>
                                                    <th class="no-sort">&nbsp;</th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TOTALES Y BOTONES -->
         <%--   <div class="row mt-3">
                <div class="col-md-8">
                    <!-- Espacio para observaciones u otros campos -->
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="row mb-2">
                                <div class="col-6 text-end">
                                    <strong>Sub Total:</strong>
                                </div>
                                <div class="col-6 text-end">
                                    <span id="lblSubtotal">Q 0.00</span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6 text-end">
                                    <strong>Total:</strong>
                                </div>
                                <div class="col-6 text-end">
                                    <span id="lblTotal" class="fs-5 fw-bold">Q 0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>--%>

            <!-- RESUMEN DE COMPRA -->
            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Resumen de Compra</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Productos agregados:</strong> <span id="totalProductos">0</span></p>
                                </div>
                                <div class="col-md-6 text-end">
                                    <p><strong>Total:</strong> Q <span id="totalCompra">0.00</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- BOTONES DE ACCIÓN -->
            <div class="row mt-3">
                <div class="col-md-12 text-center">
                    <button type="button" class="btn btn-primary me-2" id="btnGuardar">
                        <i data-feather="save" class="me-1"></i>Registrar
                    </button>
                    <button type="button" class="btn btn-danger" id="btnCancelar">
                        <i data-feather="x" class="me-1"></i>Cancelar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Estilos para alineación de DataTable */
        #grvProductosCompra td {
            vertical-align: middle !important;
            padding: 6px 4px !important;
        }

        #grvProductosCompra .form-control {
            height: 24px !important;
            padding: 2px 6px !important;
            font-size: 12px !important;
            border: 1px solid #ddd !important;
            border-radius: 3px !important;
            line-height: 1.2 !important;
        }

        #grvProductosCompra .cantidad-input {
            width: 70px !important;
            text-align: center !important;
        }

        #grvProductosCompra .costo-input,
        #grvProductosCompra .precio-input,
        #grvProductosCompra .precio-min-input {
            width: 90px !important;
            text-align: right !important;
        }

        #grvProductosCompra .costo-total {
            font-weight: 500;
            text-align: right;
            display: inline-block;
            min-width: 80px;
        }

        #grvProductosCompra .btn-sm {
            height: 26px !important;
            width: 26px !important;
            padding: 0 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 11px !important;
        }

        #grvProductosCompra .view-product img {
            width: 32px !important;
            height: 32px !important;
            object-fit: cover !important;
        }

        #grvProductosCompra .productimgname {
            display: flex !important;
            align-items: center !important;
            gap: 10px !important;
            min-width: 250px !important;
        }

        /* Ajustar ancho de columna Producto */
        #grvProductosCompra th:nth-child(3) {
            min-width: 250px !important;
        }

        /* Solo mejorar el input de cantidad para que sea más pequeño */
        #grvProductosCompra .cantidad-input {
            width: 60px !important;
            text-align: center !important;
        }
    </style>

    <script type="text/javascript">
        // Variables globales
        var tbl_productos_compra;
        var tbl_buscar_producto;
        var MONTO_IVA = 1 <%--<%= MONTO_IVA %>--%>;
        var EDIT_COMPRA = 2<%--<%= EDIT_COMPRA.ToString().ToLower() %>--%>;

        // Arreglo para almacenar los productos de la compra
        var productosCompra = [];
        var ID_COMPRA = "<%= Request.QueryString["id"] %>";

        // Función para buscar proveedor por NIT (adaptada de borrar.js)
        function BuscarProveedor(nit) {
            if (!nit) {
                nit = $('#nitProveedor').val();
            }

            if (!nit) {
                Swal.fire('Campo requerido', 'Ingrese el NIT del proveedor.', 'warning');
                return;
            }

            if (nit != "C/F" && nit != "CF") {
                __Progress("Buscando proveedor...");

                $.ajax({
                    type: 'GET',
                    url: `<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/compra.ashx?mth=${mtdEnc("get/provider/by/nit")}&nit=${mtdEnc(nit)}`,
                    success: function (response) {
                        __ProgressOff();
                        if (response.type == "success") {
                            SetDataProveedor(response.data, false);
                        } else {
                            ClearFieldProveedor();
                            Swal.fire({
                                title: 'Proveedor no encontrado',
                                text: '¿Desea registrar un nuevo proveedor?',
                                icon: 'question',
                                showCancelButton: true,
                                confirmButtonText: 'Sí, registrar',
                                cancelButtonText: 'No'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    $('#txtNitNuevoProveedor').val(nit);
                                    $('#mdlNuevoProveedor').modal('show');
                                }
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        __ProgressOff();
                        console.log('Error:', error);
                        Swal.fire('Error', 'Ocurrió un error al buscar el proveedor.', 'error');
                    }
                });
            } else {
                //Swal.fire({ icon: "info", text: "Consumidor final" });
                SetDataProveedor({
                    id_proveedor: "",
                    nit: "C/F",
                    nombres: 'CONSUMIDOR',
                    apellidos: 'FINAL',
                    direccion: 'Ciudad'
                }, true);
            }
        }

        // Función para limpiar campos de proveedor (adaptada de borrar.js)
        function ClearFieldProveedor() {
            $('#idProveedor').val('');
            $('#nitProveedor').val('');
            $('#nombreProveedor').val('');
            $('#direccionProveedor').val('');
        }

        // Función para limpiar todos los campos (adaptada de borrar.js)
        function ClearAllFields() {
            ClearFieldProveedor();
            $('#codigoCompra').val('');
            $('#polizaDuca').val('');
            $('#fechaCompra').val('');
            $('#ddlFormaPago').val('');

            // Limpiar tabla de productos
            if (tbl_productos_compra) {
                tbl_productos_compra.clear().draw();
            }

            // Limpiar totales
            $('#lblSubtotal').text('Q 0.00');
            $('#lblTotal').text('Q 0.00');
        }

        function SetDataProveedor(data, enableFields) {
            $('#idProveedor').val(data.id_proveedor);
            $('#nitProveedor').val(data.nit);
            //$('#nombreProveedor').val(data.nombres + data.apellidos);
            let nombre = '';
            if (data.nombres) nombre += data.nombres;
            if (data.apellidos) {
                if (nombre) nombre += ' '; // solo agrega espacio si ya hay nombre
                nombre += data.apellidos;
            }
            if (nombre) {
                $('#nombreProveedor').val(nombre);
            }

            $('#direccionProveedor').val(data.direccion);

            if (enableFields) {
                $('#nombreProveedor').prop('disabled', false);
                $('#direccionProveedor').prop('disabled', false);
            }
        }

        // Función para crear nuevo proveedor (adaptada de borrar.js)
        function NuevoProveedor(pData) {
            console.log("la data essss:");
            console.log(pData);
            __Progress("Creando proveedor...");


            var fmProveedor = new FormData();
            fmProveedor.append("mth", mtdEnc("create/provider"));
            fmProveedor.append("nit", mtdEnc(pData.nit));
            fmProveedor.append("nombre", mtdEnc(pData.nombre));
            fmProveedor.append("direccion", mtdEnc(pData.direccion));
            fmProveedor.append("telefono", mtdEnc(pData.telefono));

            $.ajax({
                type: 'POST',
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/compra.ashx',
                data: fmProveedor,
                contentType: false,
                processData: false,
                success: function (response) {
                    __ProgressOff();
                    if (response.type == "success") {
                        Swal.fire('Éxito', 'Proveedor creado correctamente.', 'success');
                        SetDataProveedor(response.data, false);
                        $('#mdlNuevoProveedor').modal('hide');

                        // Limpiar campos del modal
                        $('#txtNitNuevoProveedor').val('');
                        $('#txtNombreNuevoProveedor').val('');
                        $('#txtDireccionNuevoProveedor').val('');
                        $('#txtTelefonoNuevoProveedor').val('');
                    } else {
                        Swal.fire('Error', response.text, 'error');
                    }
                },
                error: function (xhr, status, error) {
                    __ProgressOff();
                    console.log('Error:', error);
                    Swal.fire('Error', 'Ocurrió un error al crear el proveedor.', 'error');
                }
            });
        }

        // Función para agregar producto a la tabla (adaptada de borrar.js)
        function AddProductoTable(dataResult) {
            var existingRow = tbl_productos_compra.rows().data().toArray().find(function(row) {
                return row.codigo === dataResult.codigo;
            });

            if (existingRow) {
                Swal.fire('Producto duplicado', 'Este producto ya está en la lista.', 'warning');
                return;
            }

            var newRow = {
                codigo: dataResult.codigo,
                producto: dataResult.producto,
                cantidad: '<input type="number" class="form-control cantidad-input" value="1" min="1" onchange="CalcularTotal(this, ' + tbl_productos_compra.rows().count() + ')" />',
                existencia: dataResult.existencia || 0,
                costo_unitario: '<input type="number" class="form-control costo-input" value="0.00" step="0.01" min="0" onchange="validarCantidadCosto(this, ' + tbl_productos_compra.rows().count() + ')" />',
                costo_total: '0.00',
                precio_venta: '<input type="number" class="form-control precio-input" value="0.00" step="0.01" min="0" onchange="validarCantidadCosto(this, ' + tbl_productos_compra.rows().count() + ')" />',
                precio_minimo: '<input type="number" class="form-control precio-min-input" value="0.00" step="0.01" min="0" onchange="validarCantidadCosto(this, ' + tbl_productos_compra.rows().count() + ')" />',
                acciones: '<button type="button" class="btn btn-danger btn-sm" onclick="EliminarProducto(this)"><i class="fa fa-trash"></i></button>'
            };

            tbl_productos_compra.row.add(newRow).draw();
        }

        // Función para calcular total (adaptada de borrar.js)
        function CalcularTotal(inputElement, index) {
            var row = $(inputElement).closest('tr');
            var cantidad = parseFloat($(row).find('.cantidad-input').val()) || 0;
            var costoUnitario = parseFloat($(row).find('.costo-input').val()) || 0;
            var costoTotal = cantidad * costoUnitario;

            $(row).find('td:eq(5)').text(costoTotal.toFixed(2));

            // Actualizar totales generales
            fnUpdateTotales();
        }

        // Función para validar cantidad y costo (adaptada de borrar.js)
        function validarCantidadCosto(inputElement, index) {
            var row = $(inputElement).closest('tr');
            var costoUnitario = parseFloat($(row).find('.costo-input').val()) || 0;
            var precioVenta = parseFloat($(row).find('.precio-input').val()) || 0;
            var precioMinimo = parseFloat($(row).find('.precio-min-input').val()) || 0;

            // Validar que precio de venta sea mayor que costo
            if (precioVenta > 0 && precioVenta < costoUnitario) {
                $(row).find('.precio-input').addClass('is-invalid');
                Swal.fire('Validación', 'El precio de venta no puede ser menor al costo unitario.', 'warning');
            } else {
                $(row).find('.precio-input').removeClass('is-invalid');
            }

            // Validar que precio mínimo sea mayor que costo
            if (precioMinimo > 0 && precioMinimo < costoUnitario) {
                $(row).find('.precio-min-input').addClass('is-invalid');
                Swal.fire('Validación', 'El precio mínimo no puede ser menor al costo unitario.', 'warning');
            } else {
                $(row).find('.precio-min-input').removeClass('is-invalid');
            }

            // Recalcular total si cambió el costo
            if ($(inputElement).hasClass('costo-input')) {
                CalcularTotal(inputElement, index);
            }
        }

        // Función para eliminar producto de la tabla
        function EliminarProducto(button) {
            var row = $(button).closest('tr');
            tbl_productos_compra.row(row).remove().draw();
            fnUpdateTotales();
        }

        // Función para actualizar los totales
        function fnUpdateTotales() {
            var subtotal = 0;

            tbl_productos_compra.rows().every(function() {
                var row = $(this.node());
                var costoTotal = parseFloat($(row).find('td:eq(5)').text()) || 0;
                subtotal += costoTotal;
            });

            var total = subtotal;

            $('#lblSubtotal').text(`Q ${__formatMoney(subtotal, 2, '.', ',')}`);
            $('#lblTotal').text(`Q ${__formatMoney(total, 2, '.', ',')}`);
        }

        // Función para guardar compra (adaptada de borrar.js)
        function GuardarCompra() {
            // Validar campos requeridos
            if (!validarCamposRequeridos()) {
                return;
            }

            // Validar que haya productos
            if (tbl_productos_compra.rows().count() === 0) {
                Swal.fire('Validación', 'Debe agregar al menos un producto a la compra.', 'warning');
                return;
            }

            // Recopilar datos de la compra
            var compraData = {
                id_proveedor: $('#idProveedor').val(),
                codigo_compra: $('#codigoCompra').val(),
                fecha_compra: $('#fechaCompra').val(),
                forma_pago: $('#ddlFormaPago').val(),
                productos: []
            };

            // Recopilar productos
            tbl_productos_compra.rows().every(function() {
                var row = $(this.node());
                var producto = {
                    codigo: $(row).find('td:eq(0)').text(),
                    cantidad: parseFloat($(row).find('.cantidad-input').val()) || 0,
                    costo_unitario: parseFloat($(row).find('.costo-input').val()) || 0,
                    precio_venta: parseFloat($(row).find('.precio-input').val()) || 0,
                    precio_minimo: parseFloat($(row).find('.precio-min-input').val()) || 0
                };
                compraData.productos.push(producto);
            });

            __Progress("Guardando compra...");

            $.ajax({
                type: 'POST',
                url: 'Compras/GuardarCompra',
                data: JSON.stringify(compraData),
                contentType: 'application/json',
                success: function (response) {
                    __ProgressOff();
                    if (response.type == "success") {
                        Swal.fire('Éxito', 'Compra guardada correctamente.', 'success').then(() => {
                            window.location.href = 'Compras.aspx';
                        });
                    } else {
                        Swal.fire('Error', response.text, 'error');
                    }
                },
                error: function (xhr, status, error) {
                    __ProgressOff();
                    console.log('Error:', error);
                    Swal.fire('Error', 'Ocurrió un error al guardar la compra.', 'error');
                }
            });
        }

        // Función para validar campos requeridos
        function validarCamposRequeridos() {
            var isValid = true;

            // Validar proveedor
            if (!$('#idProveedor').val()) {
                $('#nitProveedor').addClass('is-invalid');
                isValid = false;
            } else {
                $('#nitProveedor').removeClass('is-invalid');
            }

            // Validar fecha
            if (!$('#fechaCompra').val()) {
                $('#fechaCompra').addClass('is-invalid');
                isValid = false;
            } else {
                $('#fechaCompra').removeClass('is-invalid');
            }

            // Validar forma de pago
            if (!$('#ddlFormaPago').val()) {
                $('#ddlFormaPago').addClass('is-invalid');
                isValid = false;
            } else {
                $('#ddlFormaPago').removeClass('is-invalid');
            }

            if (!isValid) {
                Swal.fire('Campos requeridos', 'Complete todos los campos obligatorios.', 'warning');
            }

            return isValid;
        }

        // Función para lista de proveedores (placeholder)
        function ListaProveedores() {
            // Esta función se implementaría para mostrar una lista de proveedores
            Swal.fire('Información', 'Funcionalidad de lista de proveedores pendiente de implementar.', 'info');
        }

        // Función para cargar todos los productos
        function CargarProductos() {
            __Progress("Cargando productos...");

            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/products"));

            $.ajax({
                type: 'POST',
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function (response) {
                    console.log("Respuesta cargar productos:", response);

                    if (response.type === "success" && response.data) {
                        // Usar el DataTable de jQuery
                        var table = $('#grvProductosCompra').DataTable();
                        table.clear();

                        console.log("Cargando", response.data.length, "productos");

                        response.data.forEach(function(producto) {
                            var newRow = {
                                id_producto: producto.id_producto,
                                codigo: producto.codigo,
                                nombre: producto.nombre,
                                stock_actual: producto.stock_actual || 0,
                                costo_unitario: producto.costo_unitario || 0,
                                precio_unitario: producto.precio_unitario || 0,
                                min_descuento: producto.min_descuento || 0,
                                categoria: producto.categoria || "",
                                descripcion: producto.descripcion || "",
                                img_producto: producto.img_producto || ""
                            };
                            table.row.add(newRow);
                        });

                        table.draw();
                    } else {
                        Swal.fire('Sin productos', 'No se encontraron productos disponibles.', 'warning');
                    }
                },
                error: function (xhr, status, error) {
                    console.log('Error:', error);
                    Swal.fire('Error', 'Ocurrió un error al cargar los productos.', 'error');
                },
                complete: function () {
                    __ProgressOff();
                }
            });
        }



        // Función para cargar formas de pago
        function cargarFormasPago() {
            console.log("=== INICIANDO CARGA DE FORMAS DE PAGO EN NUEVACOMPRA ===");
            setTimeout(function () {
                var fmAuth = new FormData();
                fmAuth.append("mth", mtdEnc("get/medio/pago/sucursal"));
                console.log("FormData creado, método:", mtdEnc("get/medio/pago/sucursal"));

                $.ajax({
                    url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/venta.ashx',
                    type: "post",
                    contentType: false,
                    processData: false,
                    ContentType: "text/html;charset=utf-8",
                    dataType: "json",
                    data: fmAuth,
                    async: true,
                    beforeSend: function () {
                        $("#ddlFormaPago").empty().append('<option value="">Cargando formas de pago...</option>');
                    },
                    success: function (response) {
                        console.log("=== RESPUESTA COMPLETA FORMAS DE PAGO ===");
                        console.log("Tipo de respuesta:", typeof response);
                        console.log("Respuesta completa:", response);
                        console.log("response.data_tables:", response.data_tables);
                        console.log("response.data:", response.data);
                        console.log("response.type:", response.type);

                        if (response && response.data_tables && Array.isArray(response.data_tables) && response.data_tables.length > 0) {
                            console.log("Intentando llenar dropdown con data_tables");
                            $("#ddlFormaPago").empty();
                            $("#ddlFormaPago").append('<option value="">Seleccionar forma de pago</option>');

                            response.data_tables[0].forEach(function (formaPago) {
                                $("#ddlFormaPago").append('<option value="' + formaPago.id_medio_pago + '">' + formaPago.descripcion + '</option>');
                            });
                            console.log("Formas de pago cargadas:", response.data_tables.length);
                        } else {
                            console.log("Condición no cumplida, llenando con mensaje de error");
                            $("#ddlFormaPago").empty().append('<option value="">No hay formas de pago disponibles</option>');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error("=== ERROR AJAX FORMAS DE PAGO ===");
                        console.error("Status:", status);
                        console.error("Error:", error);
                        console.error("XHR:", xhr);
                        console.error("XHR responseText:", xhr.responseText);
                        $("#ddlFormaPago").empty().append('<option value="">Error al cargar formas de pago</option>');
                    },
                    complete: function () {
                        console.log("AJAX completado");
                    }
                });
            }, 50);
        }

        // Función para agregar producto a la compra
        function AgregarProductoCompra(codigo) {
            console.log("Agregando producto con código:", codigo);
            // Aquí puedes implementar la lógica para agregar el producto a la compra
            Swal.fire('Producto agregado', `Producto ${codigo} agregado a la compra`, 'success');
        }

        // Función para cargar productos usando get/products (copiada exactamente de Default.aspx)
        function GetInvProductos() {
            console.log("=== INICIANDO CARGA DE PRODUCTOS CON get/products ===");
            __Progress("Cargando productos...");

            $.ajax({
                url: `<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx?mth=${mtdEnc("get/products")}`,
                data: null,
                type: "GET",
                async: true,
                contentType: 'application/x-www-form-urlencoded; charset=utf-8',
                dataType: "json",
                beforeSend: function () {
                    console.log("Enviando petición get/products...");
                },
                success: function (result) {
                    console.log("=== RESPUESTA get/products ===");
                    console.log("Tipo de resultado:", typeof result);
                    console.log("Resultado completo:", result);
                    console.log("result.type:", result.type);
                    console.log("result.data:", result.data);

                    if (result.type == "success") {
                        console.log("Éxito - Limpiando tabla y agregando datos");
                        console.log("Primer producto de ejemplo:", result.data[0]);

                        try {
                            $('#grvProductosCompra').dataTable().fnClearTable();
                            $('#grvProductosCompra').DataTable().search("").draw();
                            $('#grvProductosCompra').dataTable().fnAddData(result.data);
                            console.log("Datos agregados a la tabla:", result.data.length, "productos");
                        } catch (error) {
                            console.error("Error al agregar datos al DataTable:", error);
                            __ProgressOff();
                        }
                    }
                    else {
                        console.error("Error en respuesta:", result);
                        Swal.fire({ icon: result.type, text: result.text });
                        __ProgressOff();
                    }
                },
                error: function (result) {
                    console.error('=== ERROR AJAX get/products ===');
                    console.error('Error completo:', result);
                    Swal.fire({
                        icon: 'error',
                        text: 'Ocurrió un error al cargar los productos.'
                    });
                    __ProgressOff();
                },
                complete: function () {
                    console.log("AJAX get/products completado");
                    __ProgressOff();
                }
            });
        }

        // Inicialización del documento
        $(document).ready(function () {
            // Inicializar DataTable para productos (copiado exactamente de Default.aspx)
            $("#grvProductosCompra").DataTable({
                "language": {
                    search: '',
                    searchPlaceholder: "Buscar producto",
                    info: "Mostrando _START_ a _END_ de _TOTAL_ registros",
                    lengthMenu: "Mostrar _MENU_ registros",
                    paginate: {
                        next: '<i class="fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                info: true,
                paging: true,
                autoWidth: true,
                //dom: 'Bfrtip',
                //dom: 'B<"top"f>rt<"bottom"lp><"clear">',
                //lengthMenu: [10, 25, 50, 100],
                buttons: [
                    {
                        extend: 'excelHtml5',
                        autoFilter: true,
                        sheetName: 'Inventario',
                        className: 'btn btn-success btn-sm',
                        //exportOptions: {
                        //    columns: [1, 2, 3, 4, 5, 6, 7, 8, 9]
                        //},
                        text: '<i class="fa fa-file-excel"></i>&nbsp;Excel',
                        attr: {
                            title: 'Exportar productos a Excel',
                        },
                        title: `Inventario - ${new Date().toLocaleDateString('es-ES')}`
                    },
                    {
                        extend: 'print',
                        className: 'btn btn-info btn-sm',
                        text: '<i class="fa fa-print"></i>&nbsp;Imprimir',
                        attr: {
                            title: 'Exportar productos a PDF',
                        },
                        title: `Inventario - ${new Date().toLocaleDateString('es-ES')}`
                    }
                ],
                columnDefs: [
                    {
                        targets: [0],
                        visible: false
                    },
                    {
                        targets: [1],
                        //ordering: false
                    },
                    {
                        targets: [2],
                        className: 'productimgname',
                        //width: '2px'
                    },
                    {
                        targets: [3, 4, 5, 6, 7, 8], // Columnas de inputs y cálculos
                        orderable: false
                    },
                    {
                        targets: [9], // Columna de acciones
                        orderable: false,
                        className: 'text-center'
                    }
                ],
                columns: [
                    { data: "id_producto" },
                    { data: "codigo" },
                    {
                        data: function (item) {
                            let img_producto = '';
                            if (!item.img_producto) {
                                img_producto = `<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png`;
                            } else {
                                img_producto = item.img_producto;
                            }
                            return `<div class="view-product me-2">
                                        <a href="#!">
                                            <img src="${img_producto}" alt="" onerror="this.onerror=null;this.src='<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png';">
                                        </a>
                                    </div>
                                    <a href="#!" class="view-info-product">${item.nombre}</a>`;
                        }
                    },
                    // Columna 3: Cantidad (input editable)
                    {
                        data: function (item) {
                            return `<input type="number" class="form-control cantidad-input" value="1" min="1" />`;
                        }
                    },
                    // Columna 4: Existencia
                    { data: "stock_actual" },
                    // Columna 5: Costo U. (input editable)
                    {
                        data: function (item) {
                            return `<input type="number" class="form-control costo-input" value="${item.costo_unitario || 0}" step="0.01" min="0" />`;
                        }
                    },
                    // Columna 6: Costo Total (calculado)
                    {
                        data: function (item) {
                            return `<span class="costo-total">${item.costo_unitario || 0}</span>`;
                        }
                    },
                    // Columna 7: Precio Venta (input editable)
                    {
                        data: function (item) {
                            return `<input type="number" class="form-control precio-input" value="${item.precio_unitario || 0}" step="0.01" min="0" />`;
                        }
                    },
                    // Columna 8: Precio mínimo (input editable)
                    {
                        data: function (item) {
                            return `<input type="number" class="form-control precio-min-input" value="${item.min_descuento || 0}" step="0.01" min="0" />`;
                        }
                    },
                    // Columna 9: Acciones
                    {
                        data: function (item) {
                            return `<button class="btn btn-primary btn-sm" type="button" title="Agregar a compra" onclick="AgregarProductoCompra('${item.codigo}')">
                                        <span class="fa fa-plus"></span>
                                    </button>`;
                        }
                    }
                ],
                rowCallback: function (row, data) {
                    $(row).find(".view-info-product").on("click", function () {
                        console.log("Ver info producto:", data);
                    });

                    // Agregar eventos para calcular costo total automáticamente
                    $(row).find('.cantidad-input, .costo-input').on('input', function() {
                        var cantidad = parseFloat($(row).find('.cantidad-input').val()) || 0;
                        var costoUnitario = parseFloat($(row).find('.costo-input').val()) || 0;
                        var costoTotal = cantidad * costoUnitario;
                        $(row).find('.costo-total').text(costoTotal.toFixed(2));
                    });
                }
            });

            // Inicializar DataTable para búsqueda de productos
            tbl_buscar_producto = $('#grvBuscarProducto').DataTable({
                "language": {
                    search: '',
                    searchPlaceholder: "Buscar producto",
                    info: "Mostrando _START_ a _END_ de _TOTAL_ registros",
                    lengthMenu: "Mostrar _MENU_ registros",
                    paginate: {
                        first: "Primero",
                        last: "Último",
                        next: "Siguiente",
                        previous: "Anterior"
                    },
                    emptyTable: "No hay productos disponibles",
                    zeroRecords: "No se encontraron productos"
                },
                "pageLength": 5,
                "ordering": true,
                "searching": true
            });

            // Establecer fecha actual
            var today = new Date().toISOString().split('T')[0];
            $('#fechaCompra').val(today);

            // Inicializar tabla vacía (no cargar todos los productos)
            console.log("=== TABLA INICIALIZADA VACÍA ===");

            // Eventos de búsqueda usando los botones originales
            $('#btnBuscarProducto').on('click', function() {
                BuscarProductoPorCodigo();
            });

            $('#codigoProducto').on('keypress', function(e) {
                if (e.which == 13) { // Enter
                    BuscarProductoPorCodigo();
                }
            });

            $('#btnCargarProductos').on('click', function() {
                CargarProductos();
            });

            // Cargar formas de pago
            console.log("ANTES DE LLAMAR cargarFormasPago()");
            cargarFormasPago();
            console.log("DESPUÉS DE LLAMAR cargarFormasPago()");

            // Eventos
            $('#btnBuscarProveedor').click(function () {
                BuscarProveedor();
            });

            $('#nitProveedor').keypress(function (e) {
                if (e.which == 13) { // Enter key
                    BuscarProveedor();
                }
            });

            // Conectar botón Registrar con función de guardar
            $('#btnGuardar').click(function () {
                GuardarCompra();
            });

            // Conectar botón Cancelar
            $('#btnCancelar').click(function () {
                LimpiarFormulario();
            });

            // Agregar botón para ver productos agregados
            $('#totalProductos').parent().append(' <button type="button" class="btn btn-sm btn-outline-info ms-2" id="btnVerProductos">Ver</button>');

            $('#btnVerProductos').click(function() {
                MostrarProductosAgregados();
            });

            $('#btnGuardarNuevoProveedor').click(function () {
                var proveedorData = {
                    nit: $('#txtNitNuevoProveedor').val(),
                    nombre: $('#txtNombreNuevoProveedor').val(),
                    direccion: $('#txtDireccionNuevoProveedor').val(),
                    telefono: $('#txtTelefonoNuevoProveedor').val()
                };

                if (!proveedorData.nit || !proveedorData.nombre) {
                    Swal.fire('Campos requeridos', 'Complete el NIT y nombre del proveedor.', 'warning');
                    return;
                }

                NuevoProveedor(proveedorData);
            });

            $('#btnGuardar').click(function () {
                GuardarCompra();
            });

            $('#btnCancelar').click(function () {
                Swal.fire({
                    title: '¿Está seguro?',
                    text: 'Se perderán todos los datos ingresados.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Sí, cancelar',
                    cancelButtonText: 'No'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = 'Compras.aspx';
                    }
                });
            });

            // Tabla inicia vacía - usar búsqueda para cargar productos
        });

        // Función para agregar producto a la compra
        function AgregarProductoCompra(codigo) {
            console.log("=== AGREGANDO PRODUCTO A COMPRA ===");
            console.log("Código:", codigo);

            // Obtener datos del producto desde el DataTable
            var datosProducto = null;
            var filaProducto = null;
            var table = $('#grvProductosCompra').DataTable();

            table.rows().every(function(index) {
                var data = this.data();
                console.log(`Fila ${index}: código = "${data.codigo}"`);
                if (data.codigo === codigo) {
                    datosProducto = data;
                    filaProducto = $(this.node());
                    console.log("¡Producto encontrado!", data);
                    return false;
                }
            });

            if (!datosProducto) {
                console.log("ERROR: No se encontró el producto con código:", codigo);
                console.log("Códigos disponibles:");
                table.rows().every(function(index) {
                    var data = this.data();
                    console.log(`  Fila ${index}: "${data.codigo}"`);
                });
                Swal.fire('Error', 'No se pudo encontrar el producto.', 'error');
                return;
            }

            // Obtener valores de los inputs de la fila
            var cantidad = parseFloat(filaProducto.find('.cantidad-input').val()) || 1;
            var costoUnitario = parseFloat(filaProducto.find('.costo-input').val()) || 0;
            var precioVenta = parseFloat(filaProducto.find('.precio-input').val()) || 0;
            var precioMinimo = parseFloat(filaProducto.find('.precio-min-input').val()) || 0;

            // Validaciones
            if (cantidad <= 0) {
                Swal.fire('Error', 'La cantidad debe ser mayor a 0.', 'error');
                return;
            }

            if (costoUnitario <= 0) {
                Swal.fire('Error', 'El costo unitario debe ser mayor a 0.', 'error');
                return;
            }

            console.log("Datos del producto:", datosProducto);
            console.log("Valores de inputs - Cantidad:", cantidad, "Costo:", costoUnitario, "Precio:", precioVenta);

            // Crear objeto producto para la compra
            var productoCompra = {
                id_producto: datosProducto.id_producto,
                id_moneda: datosProducto.id_moneda || 1,
                codigo: datosProducto.codigo,
                nombre: datosProducto.nombre,
                cantidad: cantidad,
                moneda: datosProducto.moneda || "GTQ",
                precio_venta: precioVenta,
                costo_unitario: costoUnitario,
                descripcion: datosProducto.descripcion || "",
                categoria: datosProducto.categoria || "",
                estado: true,
                existencia: datosProducto.stock_actual || 0,
                precio_unitario: precioVenta,
                precio_minimo: precioMinimo,
                recargo: 0,
                descuento: 0,
                img_producto: datosProducto.img_producto || ""
            };

            // Verificar si el producto ya está en la compra
            var productoExistente = productosCompra.find(p => p.codigo === codigo);
            if (productoExistente) {
                productoExistente.cantidad += cantidad;
                Swal.fire('Actualizado', `Se actualizó la cantidad del producto ${codigo}.`, 'success');
            } else {
                productosCompra.push(productoCompra);
                Swal.fire('Agregado', `Producto ${codigo} agregado a la compra.`, 'success');
            }

            console.log("Productos en compra:", productosCompra);
            ActualizarResumenCompra();
        }

        // Función para actualizar el resumen de la compra
        function ActualizarResumenCompra() {
            var subtotal = 0;
            var total = 0;
            var totalProductos = productosCompra.length;

            productosCompra.forEach(function(producto) {
                var costoTotal = producto.cantidad * producto.costo_unitario;
                subtotal += costoTotal;
            });

            total = subtotal; // Por ahora sin recargos ni descuentos

            console.log("Resumen compra - Productos:", totalProductos, "Subtotal:", subtotal, "Total:", total);

            // Actualizar elementos de la UI
            $('#totalProductos').text(totalProductos);
            $('#totalCompra').text(total.toFixed(2));
        }

        // Función para buscar producto por código (basada en NuevoProducto.aspx)
        function BuscarProductoPorCodigo() {
            var codigo = $('#codigoProducto').val().trim();

            if (!codigo || codigo.trim() === "") {
                Swal.fire({
                    icon: 'warning',
                    title: 'Código requerido',
                    text: 'Por favor ingrese un código de producto para buscar.'
                });
                return;
            }

            var fmProd = new FormData();
            var encodedMethod = mtdEnc("get/producto");
            fmProd.append("mth", encodedMethod);
            fmProd.append("codigo", codigo);

            console.log("Buscando producto con código:", codigo);

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                type: "post",
                contentType: false,
                processData: false,
                data: fmProd,
                dataType: "json",
                beforeSend: function () {
                    __Progress("Buscando producto...");
                },
                success: function (response) {
                    console.log("Respuesta de búsqueda de producto:", response);

                    if (response.type === "success" && response.data && response.data.length > 0) {
                        let producto = response.data[0];
                        CargarProductoEnTabla(producto);

                        Swal.fire({
                            icon: 'success',
                            title: 'Producto encontrado',
                            text: 'El producto se ha cargado en la tabla.',
                            showConfirmButton: false,
                            timer: 1500
                        });
                    }
                    else {
                        Swal.fire({
                            icon: 'info',
                            title: 'Producto no encontrado',
                            text: response.text || 'No se encontró el producto con el código especificado.'
                        });
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error AJAX buscar producto:", error, xhr.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Ocurrió un error al buscar el producto. Por favor intente nuevamente.'
                    });
                },
                complete: function () {
                    __ProgressOff();
                }
            });
        }

        // Función para cargar un producto específico en la tabla
        function CargarProductoEnTabla(producto) {
            console.log("Cargando producto en tabla:", producto);

            // Limpiar tabla primero
            var table = $('#grvProductosCompra').DataTable();
            table.clear();

            // Crear la estructura correcta para el DataTable
            var newRow = {
                id_producto: producto.id_producto,
                codigo: producto.codigo,
                nombre: producto.nombre,
                stock_actual: producto.stock_actual || 0,
                costo_unitario: producto.costo_unitario || 0,
                precio_unitario: producto.precio_unitario || 0,
                min_descuento: producto.min_descuento || 0,
                categoria: producto.categoria || "",
                descripcion: producto.descripcion || "",
                img_producto: producto.img_producto || ""
            };

            // Agregar el producto encontrado
            table.row.add(newRow).draw();

            console.log("Producto cargado en tabla exitosamente");
        }

        // Función para limpiar la tabla de productos
        function LimpiarTablaProductos() {
            var table = $('#grvProductosCompra').DataTable();
            table.clear().draw();
            console.log("Tabla de productos limpiada");
        }

        // Función para guardar la compra
        function GuardarCompra() {
            console.log("=== INICIANDO GUARDADO DE COMPRA ===");

            // Validaciones básicas
            if (productosCompra.length === 0) {
                Swal.fire('Error', 'Debe agregar al menos un producto a la compra.', 'error');
                return;
            }

            var codigoCompra = $('#codigoCompra').val().trim();
            if (!codigoCompra) {
                Swal.fire('Error', 'Debe ingresar el código de compra.', 'error');
                return;
            }

            var fechaCompra = $('#fechaCompra').val();
            if (!fechaCompra) {
                Swal.fire('Error', 'Debe seleccionar la fecha de compra.', 'error');
                return;
            }

            var formaPago = $('#ddlFormaPago').val();
            if (!formaPago) {
                Swal.fire('Error', 'Debe seleccionar una forma de pago.', 'error');
                return;
            }

            // Calcular totales
            var subtotal = 0;
            productosCompra.forEach(function(producto) {
                subtotal += producto.cantidad * producto.costo_unitario;
            });

            // Crear objeto de compra con la estructura que me especificaste
            var compraData = {
                id_contribuyente: null, // Se asignará en el servidor desde la sesión
                id_sucursal: null, // Se asignará en el servidor desde la sesión
                id_usuario: null, // Se asignará en el servidor desde la sesión
                moneda: "GTQ",
                mto_subtotal: subtotal,
                mto_recargo: 0,
                mto_descuento: 0,
                mto_total: subtotal,
                id_moneda: 1, // GTQ por defecto
                productos: productosCompra,
                medios_pago: [
                    {
                        codigo: formaPago,
                        descripcion: $('#ddlFormaPago option:selected').text(),
                        moneda: "GTQ",
                        id_medio_pago: parseInt(formaPago) || 0,
                        cod_entidad: "",
                        cod_referencia: "",
                        id_banco: 0,
                        fecha_referencia: new Date().toISOString(),
                        num_referencia: "",
                        num_autorizacion: "",
                        monto: subtotal
                    }
                ],
                encabezado: {
                    id_referencia: codigoCompra,
                    tipo_transaccion: "COMPRA",
                    fecha: fechaCompra,
                    id_caja: null, // Se asignará en el servidor o null
                    id_cliente: "" // En compras sería id_proveedor, pero adaptamos la estructura
                }
            };

            console.log("Datos de compra a enviar:", compraData);

            // Enviar al servidor
            EnviarCompraAlServidor(compraData);
        }

        // Función para enviar la compra al servidor
        function EnviarCompraAlServidor(compraData) {
            console.log("=== ENVIANDO COMPRA AL SERVIDOR ===");

            var frmCompra = new FormData();
            frmCompra.append("mth", mtdEnc("save/purchase"));
            frmCompra.append("data", mtdEnc(JSON.stringify(compraData)));

            $.ajax({
                url: `${_url_redirect}Controllers/compra.ashx?mtd=${mtdEnc("save/purchase")}`,
                data: frmCompra,
                type: "POST",
                contentType: false,
                processData: false,
                ContentType: "text/html;charset=utf-8",
                dataType: "json",
                beforeSend: function () {
                    __Progress("Guardando compra...");
                },
                success: function (response) {
                    __ProgressOff();
                    console.log("Respuesta del servidor:", response);

                    if (response.type === "success") {
                        Swal.fire({
                            title: 'Éxito',
                            text: 'La compra se guardó correctamente.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Limpiar formulario
                            LimpiarFormulario();
                        });
                    } else {
                        Swal.fire('Error', response.text || 'Error al guardar la compra.', 'error');
                    }
                },
                error: function (xhr, status, error) {
                    __ProgressOff();
                    console.error('Error AJAX:', error);
                    console.error('Response:', xhr.responseText);
                    Swal.fire('Error', 'Ocurrió un error al comunicarse con el servidor.', 'error');
                },
                complete: function () {
                    __ProgressOff();
                }
            });
        }

        // Función para limpiar el formulario después de guardar
        function LimpiarFormulario() {
            productosCompra = [];
            $('#codigoCompra').val('');
            $('#polizaDuca').val('');
            $('#fechaCompra').val('');
            $('#ddlFormaPago').val('');

            // Limpiar inputs de la tabla
            $('#grvProductosCompra tbody tr').each(function() {
                $(this).find('.cantidad-input').val('');
                $(this).find('.costo-input').val('');
                $(this).find('.precio-input').val('');
                $(this).find('.precio-min-input').val('');
                $(this).find('.costo-total').text('0.00');
            });

            console.log("Formulario limpiado");
        }



        // Función para mostrar productos agregados a la compra
        function MostrarProductosAgregados() {
            if (productosCompra.length === 0) {
                Swal.fire('Información', 'No hay productos agregados a la compra.', 'info');
                return;
            }

            var html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>Código</th><th>Producto</th><th>Cantidad</th><th>Costo U.</th><th>Total</th><th>Acción</th></tr></thead>';
            html += '<tbody>';

            productosCompra.forEach(function(producto, index) {
                var total = producto.cantidad * producto.costo_unitario;
                html += `<tr>
                    <td>${producto.codigo}</td>
                    <td>${producto.nombre}</td>
                    <td>${producto.cantidad}</td>
                    <td>Q ${producto.costo_unitario.toFixed(2)}</td>
                    <td>Q ${total.toFixed(2)}</td>
                    <td><button type="button" class="btn btn-sm btn-danger" onclick="EliminarProductoCompra(${index})">
                        <i class="fa fa-trash"></i>
                    </button></td>
                </tr>`;
            });

            html += '</tbody></table></div>';

            Swal.fire({
                title: 'Productos en la Compra',
                html: html,
                width: '80%',
                showCloseButton: true,
                showConfirmButton: false
            });
        }

        // Función para eliminar producto de la compra
        function EliminarProductoCompra(index) {
            if (index >= 0 && index < productosCompra.length) {
                var producto = productosCompra[index];

                Swal.fire({
                    title: '¿Eliminar producto?',
                    text: `¿Está seguro de eliminar "${producto.nombre}" de la compra?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Sí, eliminar',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.isConfirmed) {
                        productosCompra.splice(index, 1);
                        ActualizarResumenCompra();
                        Swal.fire('Eliminado', 'El producto fue eliminado de la compra.', 'success');
                    }
                });
            }
        }
    </script>

</asp:Content>

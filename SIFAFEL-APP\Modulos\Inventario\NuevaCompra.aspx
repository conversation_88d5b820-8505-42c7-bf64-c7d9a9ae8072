﻿<%@ Page Title="Nueva Compra" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="NuevaCompra.aspx.cs" Inherits="SIFAFEL_APP.Modulos.Inventario.NuevaCompra" %>

<%@ Register Src="~/WebUserControls/WUC_InfoProduct.ascx" TagPrefix="uc1" TagName="WUC_InfoProduct" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">
    <uc1:WUC_InfoProduct runat="server" ID="WUC_InfoProduct" />

    <%-- MODAL PROVEEDORES --%>
    <div class="modal fade effect-scale" id="modalProveedores">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h4 class="modal-title"><i data-feather="truck" class="feather-16"></i>&nbsp;Consultas de proveedores</h4>
                    <button type="button" aria-label="Close" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-start">

                    <table id="grvProveedores" class="table table-bordered table-striped table-condensed table-sm">
                        <thead class="table-head">
                            <tr>
                                <th>Código</th>
                                <th>NIT</th>
                                <th>Proveedor</th>
                                <th>Teléfono</th>
                                <th>Correo</th>
                                <th>Direccion</th>
                                <th>&nbsp;</th>
                            </tr>
                        </thead>
                    </table>

                </div>
                <div class="modal-footer d-flex justify-content-end">
                    <button type="button" class="btn btn-cancel btn-sm" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <%-- MODAL PRODUCTOS --%>
    <div class="modal fade effect-scale" id="modalProductos">
        <div class="modal-dialog modal-fullscreen" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h4 class="modal-title">Productos</h4>
                    <button type="button" aria-label="Close" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-start">

                    <table id="grvProductosModal" class="table table-bordered table-striped table-condensed table-sm">
                        <thead class="table-head">
                            <tr>
                                <th>Código</th>
                                <th>Producto</th>
                                <th>Precio</th>
                                <th>Stock</th>
                                <th>&nbsp;</th>
                            </tr>
                        </thead>
                    </table>

                </div>
                <div class="modal-footer d-flex justify-content-end">
                    <button type="button" class="btn btn-cancel btn-sm" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <%-- HEADER --%>
    <div class="page-header">
        <div class="add-item d-flex">
            <div class="page-title">
                <h4><i data-feather="shopping-bag" class="me-2"></i>Nueva compra</h4>
                <h6>Nueva compra</h6>
            </div>
        </div>
        <div class="page-btn">
            <a href="Compras.aspx" class="btn btn-added color">
                <i data-feather="inbox" class="me-2"></i>Listado de compras
            </a>
        </div>
    </div>

    <%-- COMPRAS --%>
    <div class="row compras">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">

                    <div class="row">
                        <!-- SECCION DE PROVEEDOR -->
                        <div class="col-md-8">
                            <div class="form-horizontal">

                                <!-- NIT PROVEEDOR -->
                                <div class="mb-1 row">
                                    <div class="add-newplus">
                                        <span id="lblIdProveedor" hidden=""></span>
                                        <label for="txtNitProveedor" class="form-label">Proveedor:</label>

                                        <a href="#!" id="btnBuscarProveedor">
                                            <i data-feather="search" class="plus-down-add"></i>
                                            <span>Buscar</span>
                                        </a>
                                    </div>
                                    <div class="col-12">
                                        <div class="row g-0">
                                            <div class="col-4 input-h-control">
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">
                                                        <i data-feather="truck" class="feather-16"></i>
                                                    </span>
                                                    <input id="txtNitProveedor" name="txtNitProveedor" type="text" aria-label="NIT" placeholder="NIT" class="form-control no-right-border">
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <input id="txtNombreProveedor" type="text" aria-label="Nombre completo" placeholder="Nombre completo" class="form-control no-left-border no-right-border input-h-control" disabled="disabled">
                                            </div>
                                            <div class="col-4">
                                                <div class="input-group input-group-sm">
                                                    <input id="txtFechaCompra" name="txtFechaCompra" type="date" aria-label="Fecha" class="form-control no-left-border input-h-control">
                                                    <span class="input-group-text">
                                                        <i data-feather="calendar" class="feather-16"></i>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- SEGUNDA FILA: CAMPOS ADICIONALES -->
                                <div class="mb-1 row">
                                    <div class="col-12">
                                        <div class="row g-0">
                                            <div class="col-4 input-h-control">
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">
                                                        <i data-feather="file-text" class="feather-16"></i>
                                                    </span>
                                                    <input id="txtCodigoCompra" name="txtCodigoCompra" type="text" aria-label="Código" placeholder="Código compra" class="form-control no-right-border">
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <input id="txtPolizaDuca" name="txtPolizaDuca" type="text" aria-label="Póliza/DUCA" placeholder="Póliza/DUCA" class="form-control no-left-border no-right-border input-h-control">
                                            </div>
                                            <div class="col-4">
                                                <select id="ddlFormaPago" name="ddlFormaPago" class="form-select no-left-border input-h-control">
                                                    <option value="">Forma de pago</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>


                        <!-- SECCION DE COMPRA -->
                        <div class="col-md-4">
                            <div class="form-horizontal">

                                <%-- BOTONES DE ACCIÓN --%>
                                <div class="mb-1 row">
                                    <div class="col-sm-12">
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-primary btn-sm" id="btnGuardarCompra">
                                                <i data-feather="save" class="feather-16"></i>&nbsp;Guardar Compra
                                            </button>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                        <span class="card-title" style="float: left; width: auto !important;">
                            <i data-feather="package" class="feather-16"></i>&nbsp;Productos
                        </span>

                        <%-- CODIGO DE BARRA --%>
                        <div class="col-lg-4 col-sm-12 ms-auto">
                            <div class="add-newplus">
                                <label class="form-label" for="txtCodigoBarraProducto">&nbsp;</label>
                                <a href="#!" id="btnBuscarProducto">
                                    <i data-feather="search" class="plus-down-add"></i>
                                    <span>Consultar</span>
                                </a>
                            </div>
                            <div class="input-blocks">
                                <div class="input-groupicon select-code">
                                    <input id="txtCodigoBarraProducto" name="txtCodigoBarraProducto" class="barcode-search" type="text" placeholder="Código de producto" style="padding: 10px;">
                                    <div class="addonset">
                                        <img src="<%=ConfigurationManager.AppSettings["url_cdn"]%>img/barcode-scanner.gif" alt="img" style="height: 38px;">
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <%-- PRODUCTOS COMPRA --%>
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="grvProductosCompra" class="table table-striped table-bordered table-sm">
                                <thead class="table-head">
                                    <tr>
                                        <th>Id</th>
                                        <th>Código</th>
                                        <th>Producto</th>
                                        <th>Cantidad</th>
                                        <th>Existencia</th>
                                        <th>Costo U.</th>
                                        <th>Costo Total</th>
                                        <th>Precio Venta</th>
                                        <th>Precio Mínimo</th>
                                        <th>&nbsp;</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>


    <style>
        /* Estilos para inputs sin bordes */
        .no-left-border {
            border-left: none !important;
            border-radius: 0 !important;
        }

        .no-right-border {
            border-right: none !important;
            border-radius: 0 !important;
        }

        .input-h-control {
            height: 38px;
        }

        /* Estilos para DataTable */
        #grvProductosCompra td {
            vertical-align: middle !important;
            padding: 8px 6px !important;
        }

        #grvProductosCompra .form-control {
            height: 28px !important;
            padding: 4px 8px !important;
            font-size: 12px !important;
            border: 1px solid #ced4da !important;
            border-radius: 4px !important;
        }

        #grvProductosCompra .btn-sm {
            padding: 4px 8px !important;
            font-size: 12px !important;
        }

        .productimgname img {
            width: 32px !important;
            height: 32px !important;
            object-fit: cover;
            border-radius: 4px;
        }

        .view-product {
            display: inline-block;
            vertical-align: middle;
        }

        .view-info-product {
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
            font-size: 13px;
            text-decoration: none;
            color: #495057;
        }

        .view-info-product:hover {
            color: #007bff;
            text-decoration: none;
        }
    </style>


    <script type="text/javascript">
        // Variables globales
        var tbl_productos_compra;
        var tbl_proveedores;
        var productosCompra = [];

        $(document).ready(function () {
            // Inicializar fecha actual
            $('#txtFechaCompra').val(new Date().toISOString().split('T')[0]);

            // Inicializar DataTable de productos
            inicializarDataTableProductos();

            // Inicializar DataTable de proveedores
            inicializarDataTableProveedores();

            // Cargar formas de pago
            cargarFormasPago();

            // Eventos
            $('#btnBuscarProveedor').on('click', function() {
                $('#modalProveedores').modal('show');
            });

            $('#btnBuscarProducto').on('click', function() {
                $('#modalProductos').modal('show');
            });

            $('#txtCodigoBarraProducto').on('keypress', function(e) {
                if (e.which == 13) { // Enter
                    buscarProductoPorCodigo();
                }
            });

            $('#btnGuardarCompra').on('click', function() {
                guardarCompra();
            });

            // Buscar proveedor por NIT al presionar Enter
            $('#txtNitProveedor').on('keypress', function(e) {
                if (e.which == 13) { // Enter
                    buscarProveedorPorNit();
                }
            });
        });
        // Función para inicializar DataTable de productos
        function inicializarDataTableProductos() {
            tbl_productos_compra = $('#grvProductosCompra').DataTable({
                responsive: true,
                processing: true,
                language: {
                    url: '<%=ConfigurationManager.AppSettings["url_cdn"] %>vendor/datatables/Spanish.json'
                },
                columnDefs: [
                    {
                        targets: [0],
                        visible: false
                    },
                    {
                        targets: [3, 5, 6, 7, 8], // Columnas editables
                        orderable: false
                    },
                    {
                        targets: [9], // Columna de acciones
                        orderable: false,
                        className: 'text-center'
                    }
                ],
                columns: [
                    { data: "id_producto" },
                    { data: "codigo" },
                    {
                        data: function (item) {
                            let img_producto = '';
                            if (!item.img_producto) {
                                img_producto = `<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png`;
                            } else {
                                img_producto = item.img_producto;
                            }
                            return `<div class="view-product me-2">
                                        <a href="#!">
                                            <img src="${img_producto}" alt="" onerror="this.onerror=null;this.src='<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png';">
                                        </a>
                                    </div>
                                    <a href="#!" class="view-info-product">${item.nombre}</a>`;
                        }
                    },
                    // Columna 3: Cantidad (input editable)
                    {
                        data: function (item) {
                            return `<input type="number" class="form-control cantidad-input" value="1" min="1" onkeyup="validarCantidadCosto(this)" />`;
                        }
                    },
                    // Columna 4: Existencia
                    { data: "stock_actual" },
                    // Columna 5: Costo U. (input editable)
                    {
                        data: function (item) {
                            return `<input type="number" class="form-control costo-input" value="${item.costo_unitario || 0}" step="0.01" min="0" onkeyup="validarCantidadCosto(this)" />`;
                        }
                    },
                    // Columna 6: Costo Total (calculado)
                    {
                        data: function (item) {
                            return `<span class="costo-total">${item.costo_unitario || 0}</span>`;
                        }
                    },
                    // Columna 7: Precio Venta (input editable)
                    {
                        data: function (item) {
                            return `<input type="number" class="form-control precio-input" value="${item.precio_unitario || 0}" step="0.01" min="0" onkeyup="validarPrecios(this)" />`;
                        }
                    },
                    // Columna 8: Precio mínimo (input editable)
                    {
                        data: function (item) {
                            return `<input type="number" class="form-control precio-min-input" value="${item.min_descuento || 0}" step="0.01" min="0" onkeyup="validarPrecios(this)" />`;
                        }
                    },
                    // Columna 9: Acciones
                    {
                        data: function (item) {
                            return `<button class="btn btn-primary btn-sm" type="button" title="Agregar a compra" onclick="AgregarProductoCompra('${item.codigo}')">
                                        <span class="fa fa-plus"></span>
                                    </button>`;
                        }
                    }
                ],
                rowCallback: function (row, data) {
                    $(row).find(".view-info-product").on("click", function () {
                        console.log("Ver info producto:", data);
                    });

                    // Agregar eventos para calcular costo total automáticamente
                    $(row).find('.cantidad-input, .costo-input').on('input', function() {
                        var cantidad = parseFloat($(row).find('.cantidad-input').val()) || 0;
                        var costoUnitario = parseFloat($(row).find('.costo-input').val()) || 0;
                        var costoTotal = cantidad * costoUnitario;
                        $(row).find('.costo-total').text(costoTotal.toFixed(2));
                    });
                }
            });
        }
        // Función para inicializar DataTable de proveedores
        function inicializarDataTableProveedores() {
            tbl_proveedores = $('#grvProveedores').DataTable({
                responsive: true,
                processing: true,
                language: {
                    url: '<%=ConfigurationManager.AppSettings["url_cdn"] %>vendor/datatables/Spanish.json'
                },
                ajax: {
                    url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                    type: 'POST',
                    data: function(d) {
                        var fmAuth = new FormData();
                        fmAuth.append("mth", mtdEnc("get/proveedores"));
                        fmAuth.append("data", mtdEnc(JSON.stringify({})));
                        return fmAuth;
                    },
                    contentType: false,
                    processData: false,
                    dataSrc: function(json) {
                        return json.data || [];
                    }
                },
                columns: [
                    { data: "id_proveedor" },
                    { data: "nit" },
                    { data: "nombres" },
                    { data: "telefono" },
                    { data: "correo" },
                    { data: "direccion" },
                    {
                        data: function(item) {
                            return `<button class="btn btn-primary btn-sm" onclick="seleccionarProveedor('${item.id_proveedor}', '${item.nit}', '${item.nombres}')">
                                        <i class="fa fa-check"></i>
                                    </button>`;
                        }
                    }
                ]
            });
        }
        // Función para buscar proveedor por NIT
        function buscarProveedorPorNit() {
            var nit = $('#txtNitProveedor').val().trim();
            if (!nit) {
                Swal.fire('Campo requerido', 'Ingrese el NIT del proveedor.', 'warning');
                return;
            }

            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/proveedor/by/nit"));
            fmAuth.append("nit", mtdEnc(nit));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response.type === "success" && response.data) {
                        $('#lblIdProveedor').text(response.data.id_proveedor);
                        $('#txtNombreProveedor').val(response.data.nombres);
                    } else {
                        Swal.fire('Proveedor no encontrado', 'No se encontró un proveedor con el NIT especificado.', 'warning');
                        $('#txtNombreProveedor').val('');
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Ocurrió un error al buscar el proveedor.', 'error');
                }
            });
        }

        // Función para seleccionar proveedor del modal
        function seleccionarProveedor(id, nit, nombre) {
            $('#lblIdProveedor').text(id);
            $('#txtNitProveedor').val(nit);
            $('#txtNombreProveedor').val(nombre);
            $('#modalProveedores').modal('hide');
        }

        // Función para cargar formas de pago
        function cargarFormasPago() {
            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/medio/pago/sucursal"));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response.type === "success" && response.data) {
                        $('#ddlFormaPago').empty().append('<option value="">Forma de pago</option>');
                        response.data.forEach(function(item) {
                            $('#ddlFormaPago').append(`<option value="${item.id_medio_pago}">${item.descripcion}</option>`);
                        });
                    }
                },
                error: function() {
                    console.log('Error al cargar formas de pago');
                }
            });
        }
        // Función para buscar producto por código
        function buscarProductoPorCodigo() {
            var codigo = $('#txtCodigoBarraProducto').val().trim();
            if (!codigo) {
                Swal.fire('Campo requerido', 'Ingrese el código del producto.', 'warning');
                return;
            }

            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/producto"));
            fmAuth.append("codigo", mtdEnc(codigo));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response.type === "success" && response.data && response.data.length > 0) {
                        var producto = response.data[0];
                        tbl_productos_compra.row.add(producto).draw();
                        $('#txtCodigoBarraProducto').val('');
                    } else {
                        Swal.fire('Producto no encontrado', 'No se encontró el producto con el código especificado.', 'warning');
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Ocurrió un error al buscar el producto.', 'error');
                }
            });
        }

        // Función para validar precios (basada en NuevoProducto.aspx)
        function validarPrecios(input) {
            var $row = $(input).closest('tr');
            var costoUnitario = parseFloat($row.find('.costo-input').val()) || 0;
            var precio = parseFloat($(input).val()) || 0;

            if (precio > 0 && costoUnitario > 0 && precio < costoUnitario) {
                Swal.fire({
                    icon: 'warning',
                    text: 'El precio no debe ser menor al costo del producto.',
                });
                $(input).val(costoUnitario);
            }
        }

        // Función para validar cantidad y costo
        function validarCantidadCosto(input) {
            var $row = $(input).closest('tr');
            var cantidad = parseFloat($row.find('.cantidad-input').val()) || 0;
            var costoUnitario = parseFloat($row.find('.costo-input').val()) || 0;
            var costoTotal = cantidad * costoUnitario;
            $row.find('.costo-total').text(costoTotal.toFixed(2));
        }
        // Función para agregar producto a la compra
        function AgregarProductoCompra(codigo) {
            // Aquí puedes agregar la lógica para agregar el producto a la compra
            console.log('Agregando producto:', codigo);
        }

        // Función para guardar compra
        function guardarCompra() {
            // Validar campos requeridos
            if (!$('#txtNitProveedor').val()) {
                Swal.fire('Campo requerido', 'Seleccione un proveedor.', 'warning');
                return;
            }

            if (!$('#txtFechaCompra').val()) {
                Swal.fire('Campo requerido', 'Ingrese la fecha de compra.', 'warning');
                return;
            }

            // Aquí puedes agregar la lógica para guardar la compra
            Swal.fire('Éxito', 'Compra guardada correctamente.', 'success');
        }
    </script>

</asp:Content>



            var newRow = {
                codigo: dataResult.codigo,
                producto: dataResult.producto,
                cantidad: '<input type="number" class="form-control cantidad-input" value="1" min="1" onchange="CalcularTotal(this, ' + tbl_productos_compra.rows().count() + ')" />',
                existencia: dataResult.existencia || 0,
                costo_unitario: '<input type="number" class="form-control costo-input" value="0.00" step="0.01" min="0" onchange="validarCantidadCosto(this, ' + tbl_productos_compra.rows().count() + ')" />',
                costo_total: '0.00',
                precio_venta: '<input type="number" class="form-control precio-input" value="0.00" step="0.01" min="0" onchange="validarCantidadCosto(this, ' + tbl_productos_compra.rows().count() + ')" />',
                precio_minimo: '<input type="number" class="form-control precio-min-input" value="0.00" step="0.01" min="0" onchange="validarCantidadCosto(this, ' + tbl_productos_compra.rows().count() + ')" />',
                acciones: '<button type="button" class="btn btn-danger btn-sm" onclick="EliminarProducto(this)"><i class="fa fa-trash"></i></button>'
            };

            tbl_productos_compra.row.add(newRow).draw();
        }

        // Función para calcular total (adaptada de borrar.js)
        function CalcularTotal(inputElement, index) {
            var row = $(inputElement).closest('tr');
            var cantidad = parseFloat($(row).find('.cantidad-input').val()) || 0;
            var costoUnitario = parseFloat($(row).find('.costo-input').val()) || 0;
            var costoTotal = cantidad * costoUnitario;

            $(row).find('td:eq(5)').text(costoTotal.toFixed(2));

            // Actualizar totales generales
            fnUpdateTotales();
        }

        // Función para validar cantidad y costo (adaptada de borrar.js)
        function validarCantidadCosto(inputElement, index) {
            var row = $(inputElement).closest('tr');
            var costoUnitario = parseFloat($(row).find('.costo-input').val()) || 0;
            var precioVenta = parseFloat($(row).find('.precio-input').val()) || 0;
            var precioMinimo = parseFloat($(row).find('.precio-min-input').val()) || 0;

            // Validar que precio de venta sea mayor que costo
            if (precioVenta > 0 && precioVenta < costoUnitario) {
                $(row).find('.precio-input').addClass('is-invalid');
                Swal.fire('Validación', 'El precio de venta no puede ser menor al costo unitario.', 'warning');
            } else {
                $(row).find('.precio-input').removeClass('is-invalid');
            }

            // Validar que precio mínimo sea mayor que costo
            if (precioMinimo > 0 && precioMinimo < costoUnitario) {
                $(row).find('.precio-min-input').addClass('is-invalid');
                Swal.fire('Validación', 'El precio mínimo no puede ser menor al costo unitario.', 'warning');
            } else {
                $(row).find('.precio-min-input').removeClass('is-invalid');
            }

            // Recalcular total si cambió el costo
            if ($(inputElement).hasClass('costo-input')) {
                CalcularTotal(inputElement, index);
            }
        }

        // Función para eliminar producto de la tabla
        function EliminarProducto(button) {
            var row = $(button).closest('tr');
            tbl_productos_compra.row(row).remove().draw();
            fnUpdateTotales();
        }

        // Función para actualizar los totales
        function fnUpdateTotales() {
            var subtotal = 0;

            tbl_productos_compra.rows().every(function() {
                var row = $(this.node());
                var costoTotal = parseFloat($(row).find('td:eq(5)').text()) || 0;
                subtotal += costoTotal;
            });

            var total = subtotal;

            $('#lblSubtotal').text(`Q ${__formatMoney(subtotal, 2, '.', ',')}`);
            $('#lblTotal').text(`Q ${__formatMoney(total, 2, '.', ',')}`);
        }

        // Función para guardar compra (adaptada de borrar.js)
        function GuardarCompra() {
            // Validar campos requeridos
            if (!validarCamposRequeridos()) {
                return;
            }

            // Validar que haya productos
            if (tbl_productos_compra.rows().count() === 0) {
                Swal.fire('Validación', 'Debe agregar al menos un producto a la compra.', 'warning');
                return;
            }

            // Recopilar datos de la compra
            var compraData = {
                id_proveedor: $('#idProveedor').val(),
                codigo_compra: $('#codigoCompra').val(),
                fecha_compra: $('#fechaCompra').val(),
                forma_pago: $('#ddlFormaPago').val(),
                productos: []
            };

            // Recopilar productos
            tbl_productos_compra.rows().every(function() {
                var row = $(this.node());
                var producto = {
                    codigo: $(row).find('td:eq(0)').text(),
                    cantidad: parseFloat($(row).find('.cantidad-input').val()) || 0,
                    costo_unitario: parseFloat($(row).find('.costo-input').val()) || 0,
                    precio_venta: parseFloat($(row).find('.precio-input').val()) || 0,
                    precio_minimo: parseFloat($(row).find('.precio-min-input').val()) || 0
                };
                compraData.productos.push(producto);
            });

            __Progress("Guardando compra...");

            $.ajax({
                type: 'POST',
                url: 'Compras/GuardarCompra',
                data: JSON.stringify(compraData),
                contentType: 'application/json',
                success: function (response) {
                    __ProgressOff();
                    if (response.type == "success") {
                        Swal.fire('Éxito', 'Compra guardada correctamente.', 'success').then(() => {
                            window.location.href = 'Compras.aspx';
                        });
                    } else {
                        Swal.fire('Error', response.text, 'error');
                    }
                },
                error: function (xhr, status, error) {
                    __ProgressOff();
                    console.log('Error:', error);
                    Swal.fire('Error', 'Ocurrió un error al guardar la compra.', 'error');
                }
            });
        }

        // Función para validar campos requeridos
        function validarCamposRequeridos() {
            var isValid = true;

            // Validar proveedor
            if (!$('#idProveedor').val()) {
                $('#nitProveedor').addClass('is-invalid');
                isValid = false;
            } else {
                $('#nitProveedor').removeClass('is-invalid');
            }

            // Validar fecha
            if (!$('#fechaCompra').val()) {
                $('#fechaCompra').addClass('is-invalid');
                isValid = false;
            } else {
                $('#fechaCompra').removeClass('is-invalid');
            }

            // Validar forma de pago
            if (!$('#ddlFormaPago').val()) {
                $('#ddlFormaPago').addClass('is-invalid');
                isValid = false;
            } else {
                $('#ddlFormaPago').removeClass('is-invalid');
            }

            if (!isValid) {
                Swal.fire('Campos requeridos', 'Complete todos los campos obligatorios.', 'warning');
            }

            return isValid;
        }

        // Función para lista de proveedores (placeholder)
        function ListaProveedores() {
            // Esta función se implementaría para mostrar una lista de proveedores
            Swal.fire('Información', 'Funcionalidad de lista de proveedores pendiente de implementar.', 'info');
        }

        // Función para cargar todos los productos
        function CargarProductos() {
            __Progress("Cargando productos...");

            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/products"));

            $.ajax({
                type: 'POST',
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function (response) {
                    console.log("Respuesta cargar productos:", response);

                    if (response.type === "success" && response.data) {
                        // Usar el DataTable de jQuery
                        var table = $('#grvProductosCompra').DataTable();
                        table.clear();

                        console.log("Cargando", response.data.length, "productos");

                        response.data.forEach(function(producto) {
                            var newRow = {
                                id_producto: producto.id_producto,
                                codigo: producto.codigo,
                                nombre: producto.nombre,
                                stock_actual: producto.stock_actual || 0,
                                costo_unitario: producto.costo_unitario || 0,
                                precio_unitario: producto.precio_unitario || 0,
                                min_descuento: producto.min_descuento || 0,
                                categoria: producto.categoria || "",
                                descripcion: producto.descripcion || "",
                                img_producto: producto.img_producto || ""
                            };
                            table.row.add(newRow);
                        });

                        table.draw();
                    } else {
                        Swal.fire('Sin productos', 'No se encontraron productos disponibles.', 'warning');
                    }
                },
                error: function (xhr, status, error) {
                    console.log('Error:', error);
                    Swal.fire('Error', 'Ocurrió un error al cargar los productos.', 'error');
                },
                complete: function () {
                    __ProgressOff();
                }
            });
        }



        // Función para cargar formas de pago
        function cargarFormasPago() {
            console.log("=== INICIANDO CARGA DE FORMAS DE PAGO EN NUEVACOMPRA ===");
            setTimeout(function () {
                var fmAuth = new FormData();
                fmAuth.append("mth", mtdEnc("get/medio/pago/sucursal"));
                console.log("FormData creado, método:", mtdEnc("get/medio/pago/sucursal"));

                $.ajax({
                    url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/venta.ashx',
                    type: "post",
                    contentType: false,
                    processData: false,
                    ContentType: "text/html;charset=utf-8",
                    dataType: "json",
                    data: fmAuth,
                    async: true,
                    beforeSend: function () {
                        $("#ddlFormaPago").empty().append('<option value="">Cargando formas de pago...</option>');
                    },
                    success: function (response) {
                        console.log("=== RESPUESTA COMPLETA FORMAS DE PAGO ===");
                        console.log("Tipo de respuesta:", typeof response);
                        console.log("Respuesta completa:", response);
                        console.log("response.data_tables:", response.data_tables);
                        console.log("response.data:", response.data);
                        console.log("response.type:", response.type);

                        if (response && response.data_tables && Array.isArray(response.data_tables) && response.data_tables.length > 0) {
                            console.log("Intentando llenar dropdown con data_tables");
                            $("#ddlFormaPago").empty();
                            $("#ddlFormaPago").append('<option value="">Seleccionar forma de pago</option>');

                            response.data_tables[0].forEach(function (formaPago) {
                                $("#ddlFormaPago").append('<option value="' + formaPago.id_medio_pago + '">' + formaPago.descripcion + '</option>');
                            });
                            console.log("Formas de pago cargadas:", response.data_tables.length);
                        } else {
                            console.log("Condición no cumplida, llenando con mensaje de error");
                            $("#ddlFormaPago").empty().append('<option value="">No hay formas de pago disponibles</option>');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error("=== ERROR AJAX FORMAS DE PAGO ===");
                        console.error("Status:", status);
                        console.error("Error:", error);
                        console.error("XHR:", xhr);
                        console.error("XHR responseText:", xhr.responseText);
                        $("#ddlFormaPago").empty().append('<option value="">Error al cargar formas de pago</option>');
                    },
                    complete: function () {
                        console.log("AJAX completado");
                    }
                });
            }, 50);
        }

        // Función para agregar producto a la compra
        function AgregarProductoCompra(codigo) {
            console.log("Agregando producto con código:", codigo);
            // Aquí puedes implementar la lógica para agregar el producto a la compra
            Swal.fire('Producto agregado', `Producto ${codigo} agregado a la compra`, 'success');
        }

        // Función para cargar productos usando get/products (copiada exactamente de Default.aspx)
        function GetInvProductos() {
            console.log("=== INICIANDO CARGA DE PRODUCTOS CON get/products ===");
            __Progress("Cargando productos...");

            $.ajax({
                url: `<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx?mth=${mtdEnc("get/products")}`,
                data: null,
                type: "GET",
                async: true,
                contentType: 'application/x-www-form-urlencoded; charset=utf-8',
                dataType: "json",
                beforeSend: function () {
                    console.log("Enviando petición get/products...");
                },
                success: function (result) {
                    console.log("=== RESPUESTA get/products ===");
                    console.log("Tipo de resultado:", typeof result);
                    console.log("Resultado completo:", result);
                    console.log("result.type:", result.type);
                    console.log("result.data:", result.data);

                    if (result.type == "success") {
                        console.log("Éxito - Limpiando tabla y agregando datos");
                        console.log("Primer producto de ejemplo:", result.data[0]);

                        try {
                            $('#grvProductosCompra').dataTable().fnClearTable();
                            $('#grvProductosCompra').DataTable().search("").draw();
                            $('#grvProductosCompra').dataTable().fnAddData(result.data);
                            console.log("Datos agregados a la tabla:", result.data.length, "productos");
                        } catch (error) {
                            console.error("Error al agregar datos al DataTable:", error);
                            __ProgressOff();
                        }
                    }
                    else {
                        console.error("Error en respuesta:", result);
                        Swal.fire({ icon: result.type, text: result.text });
                        __ProgressOff();
                    }
                },
                error: function (result) {
                    console.error('=== ERROR AJAX get/products ===');
                    console.error('Error completo:', result);
                    Swal.fire({
                        icon: 'error',
                        text: 'Ocurrió un error al cargar los productos.'
                    });
                    __ProgressOff();
                },
                complete: function () {
                    console.log("AJAX get/products completado");
                    __ProgressOff();
                }
            });
        }

        // Inicialización del documento
        $(document).ready(function () {
            // Inicializar DataTable para productos (copiado exactamente de Default.aspx)
            $("#grvProductosCompra").DataTable({
                "language": {
                    search: '',
                    searchPlaceholder: "Buscar producto",
                    info: "Mostrando _START_ a _END_ de _TOTAL_ registros",
                    lengthMenu: "Mostrar _MENU_ registros",
                    paginate: {
                        next: '<i class="fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                info: true,
                paging: true,
                autoWidth: true,
                //dom: 'Bfrtip',
                //dom: 'B<"top"f>rt<"bottom"lp><"clear">',
                //lengthMenu: [10, 25, 50, 100],
                buttons: [
                    {
                        extend: 'excelHtml5',
                        autoFilter: true,
                        sheetName: 'Inventario',
                        className: 'btn btn-success btn-sm',
                        //exportOptions: {
                        //    columns: [1, 2, 3, 4, 5, 6, 7, 8, 9]
                        //},
                        text: '<i class="fa fa-file-excel"></i>&nbsp;Excel',
                        attr: {
                            title: 'Exportar productos a Excel',
                        },
                        title: `Inventario - ${new Date().toLocaleDateString('es-ES')}`
                    },
                    {
                        extend: 'print',
                        className: 'btn btn-info btn-sm',
                        text: '<i class="fa fa-print"></i>&nbsp;Imprimir',
                        attr: {
                            title: 'Exportar productos a PDF',
                        },
                        title: `Inventario - ${new Date().toLocaleDateString('es-ES')}`
                    }
                ],
                columnDefs: [
                    {
                        targets: [0],
                        visible: false
                    },
                    {
                        targets: [1],
                        //ordering: false
                    },
                    {
                        targets: [2],
                        className: 'productimgname',
                        //width: '2px'
                    },
                    {
                        targets: [3, 4, 5, 6, 7, 8], // Columnas de inputs y cálculos
                        orderable: false
                    },
                    {
                        targets: [9], // Columna de acciones
                        orderable: false,
                        className: 'text-center'
                    }
                ],
                columns: [
                    { data: "id_producto" },
                    { data: "codigo" },
                    {
                        data: function (item) {
                            let img_producto = '';
                            if (!item.img_producto) {
                                img_producto = `<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png`;
                            } else {
                                img_producto = item.img_producto;
                            }
                            return `<div class="view-product me-2">
                                        <a href="#!">
                                            <img src="${img_producto}" alt="" onerror="this.onerror=null;this.src='<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png';">
                                        </a>
                                    </div>
                                    <a href="#!" class="view-info-product">${item.nombre}</a>`;
                        }
                    },
                    // Columna 3: Cantidad (input editable)
                    {
                        data: function (item) {
                            return `<input type="number" class="form-control cantidad-input" value="1" min="1" />`;
                        }
                    },
                    // Columna 4: Existencia
                    { data: "stock_actual" },
                    // Columna 5: Costo U. (input editable)
                    {
                        data: function (item) {
                            return `<input type="number" class="form-control costo-input" value="${item.costo_unitario || 0}" step="0.01" min="0" />`;
                        }
                    },
                    // Columna 6: Costo Total (calculado)
                    {
                        data: function (item) {
                            return `<span class="costo-total">${item.costo_unitario || 0}</span>`;
                        }
                    },
                    // Columna 7: Precio Venta (input editable)
                    {
                        data: function (item) {
                            return `<input type="number" class="form-control precio-input" value="${item.precio_unitario || 0}" step="0.01" min="0" />`;
                        }
                    },
                    // Columna 8: Precio mínimo (input editable)
                    {
                        data: function (item) {
                            return `<input type="number" class="form-control precio-min-input" value="${item.min_descuento || 0}" step="0.01" min="0" />`;
                        }
                    },
                    // Columna 9: Acciones
                    {
                        data: function (item) {
                            return `<button class="btn btn-primary btn-sm" type="button" title="Agregar a compra" onclick="AgregarProductoCompra('${item.codigo}')">
                                        <span class="fa fa-plus"></span>
                                    </button>`;
                        }
                    }
                ],
                rowCallback: function (row, data) {
                    $(row).find(".view-info-product").on("click", function () {
                        console.log("Ver info producto:", data);
                    });

                    // Agregar eventos para calcular costo total automáticamente
                    $(row).find('.cantidad-input, .costo-input').on('input', function() {
                        var cantidad = parseFloat($(row).find('.cantidad-input').val()) || 0;
                        var costoUnitario = parseFloat($(row).find('.costo-input').val()) || 0;
                        var costoTotal = cantidad * costoUnitario;
                        $(row).find('.costo-total').text(costoTotal.toFixed(2));
                    });
                }
            });

            // Inicializar DataTable para búsqueda de productos
            tbl_buscar_producto = $('#grvBuscarProducto').DataTable({
                "language": {
                    search: '',
                    searchPlaceholder: "Buscar producto",
                    info: "Mostrando _START_ a _END_ de _TOTAL_ registros",
                    lengthMenu: "Mostrar _MENU_ registros",
                    paginate: {
                        first: "Primero",
                        last: "Último",
                        next: "Siguiente",
                        previous: "Anterior"
                    },
                    emptyTable: "No hay productos disponibles",
                    zeroRecords: "No se encontraron productos"
                },
                "pageLength": 5,
                "ordering": true,
                "searching": true
            });

            // Establecer fecha actual
            var today = new Date().toISOString().split('T')[0];
            $('#fechaCompra').val(today);

            // Inicializar tabla vacía (no cargar todos los productos)
            console.log("=== TABLA INICIALIZADA VACÍA ===");

            // Eventos de búsqueda usando los botones originales
            $('#btnBuscarProducto').on('click', function() {
                BuscarProductoPorCodigo();
            });

            $('#codigoProducto').on('keypress', function(e) {
                if (e.which == 13) { // Enter
                    BuscarProductoPorCodigo();
                }
            });

            $('#btnCargarProductos').on('click', function() {
                CargarProductos();
            });

            // Cargar formas de pago
            console.log("ANTES DE LLAMAR cargarFormasPago()");
            cargarFormasPago();
            console.log("DESPUÉS DE LLAMAR cargarFormasPago()");

            // Eventos
            $('#btnBuscarProveedor').click(function () {
                BuscarProveedor();
            });

            $('#nitProveedor').keypress(function (e) {
                if (e.which == 13) { // Enter key
                    BuscarProveedor();
                }
            });

            // Conectar botón Registrar con función de guardar
            $('#btnGuardar').click(function () {
                GuardarCompra();
            });

            // Conectar botón Cancelar
            $('#btnCancelar').click(function () {
                LimpiarFormulario();
            });

            // Agregar botón para ver productos agregados
            $('#totalProductos').parent().append(' <button type="button" class="btn btn-sm btn-outline-info ms-2" id="btnVerProductos">Ver</button>');

            $('#btnVerProductos').click(function() {
                MostrarProductosAgregados();
            });

            $('#btnGuardarNuevoProveedor').click(function () {
                var proveedorData = {
                    nit: $('#txtNitNuevoProveedor').val(),
                    nombre: $('#txtNombreNuevoProveedor').val(),
                    direccion: $('#txtDireccionNuevoProveedor').val(),
                    telefono: $('#txtTelefonoNuevoProveedor').val()
                };

                if (!proveedorData.nit || !proveedorData.nombre) {
                    Swal.fire('Campos requeridos', 'Complete el NIT y nombre del proveedor.', 'warning');
                    return;
                }

                NuevoProveedor(proveedorData);
            });

            $('#btnGuardar').click(function () {
                GuardarCompra();
            });

            $('#btnCancelar').click(function () {
                Swal.fire({
                    title: '¿Está seguro?',
                    text: 'Se perderán todos los datos ingresados.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Sí, cancelar',
                    cancelButtonText: 'No'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = 'Compras.aspx';
                    }
                });
            });

            // Tabla inicia vacía - usar búsqueda para cargar productos
        });

        // Función para agregar producto a la compra
        function AgregarProductoCompra(codigo) {
            console.log("=== AGREGANDO PRODUCTO A COMPRA ===");
            console.log("Código:", codigo);

            // Obtener datos del producto desde el DataTable
            var datosProducto = null;
            var filaProducto = null;
            var table = $('#grvProductosCompra').DataTable();

            table.rows().every(function(index) {
                var data = this.data();
                console.log(`Fila ${index}: código = "${data.codigo}"`);
                if (data.codigo === codigo) {
                    datosProducto = data;
                    filaProducto = $(this.node());
                    console.log("¡Producto encontrado!", data);
                    return false;
                }
            });

            if (!datosProducto) {
                console.log("ERROR: No se encontró el producto con código:", codigo);
                console.log("Códigos disponibles:");
                table.rows().every(function(index) {
                    var data = this.data();
                    console.log(`  Fila ${index}: "${data.codigo}"`);
                });
                Swal.fire('Error', 'No se pudo encontrar el producto.', 'error');
                return;
            }

            // Obtener valores de los inputs de la fila
            var cantidad = parseFloat(filaProducto.find('.cantidad-input').val()) || 1;
            var costoUnitario = parseFloat(filaProducto.find('.costo-input').val()) || 0;
            var precioVenta = parseFloat(filaProducto.find('.precio-input').val()) || 0;
            var precioMinimo = parseFloat(filaProducto.find('.precio-min-input').val()) || 0;

            // Validaciones
            if (cantidad <= 0) {
                Swal.fire('Error', 'La cantidad debe ser mayor a 0.', 'error');
                return;
            }

            if (costoUnitario <= 0) {
                Swal.fire('Error', 'El costo unitario debe ser mayor a 0.', 'error');
                return;
            }

            console.log("Datos del producto:", datosProducto);
            console.log("Valores de inputs - Cantidad:", cantidad, "Costo:", costoUnitario, "Precio:", precioVenta);

            // Crear objeto producto para la compra
            var productoCompra = {
                id_producto: datosProducto.id_producto,
                id_moneda: datosProducto.id_moneda || 1,
                codigo: datosProducto.codigo,
                nombre: datosProducto.nombre,
                cantidad: cantidad,
                moneda: datosProducto.moneda || "GTQ",
                precio_venta: precioVenta,
                costo_unitario: costoUnitario,
                descripcion: datosProducto.descripcion || "",
                categoria: datosProducto.categoria || "",
                estado: true,
                existencia: datosProducto.stock_actual || 0,
                precio_unitario: precioVenta,
                precio_minimo: precioMinimo,
                recargo: 0,
                descuento: 0,
                img_producto: datosProducto.img_producto || ""
            };

            // Verificar si el producto ya está en la compra
            var productoExistente = productosCompra.find(p => p.codigo === codigo);
            if (productoExistente) {
                productoExistente.cantidad += cantidad;
                Swal.fire('Actualizado', `Se actualizó la cantidad del producto ${codigo}.`, 'success');
            } else {
                productosCompra.push(productoCompra);
                Swal.fire('Agregado', `Producto ${codigo} agregado a la compra.`, 'success');
            }

            console.log("Productos en compra:", productosCompra);
            ActualizarResumenCompra();
        }

        // Función para actualizar el resumen de la compra
        function ActualizarResumenCompra() {
            var subtotal = 0;
            var total = 0;
            var totalProductos = productosCompra.length;

            productosCompra.forEach(function(producto) {
                var costoTotal = producto.cantidad * producto.costo_unitario;
                subtotal += costoTotal;
            });

            total = subtotal; // Por ahora sin recargos ni descuentos

            console.log("Resumen compra - Productos:", totalProductos, "Subtotal:", subtotal, "Total:", total);

            // Actualizar elementos de la UI
            $('#totalProductos').text(totalProductos);
            $('#totalCompra').text(total.toFixed(2));
        }

        // Función para buscar producto por código (basada en NuevoProducto.aspx)
        function BuscarProductoPorCodigo() {
            var codigo = $('#codigoProducto').val().trim();

            if (!codigo || codigo.trim() === "") {
                Swal.fire({
                    icon: 'warning',
                    title: 'Código requerido',
                    text: 'Por favor ingrese un código de producto para buscar.'
                });
                return;
            }

            var fmProd = new FormData();
            var encodedMethod = mtdEnc("get/producto");
            fmProd.append("mth", encodedMethod);
            fmProd.append("codigo", codigo);

            console.log("Buscando producto con código:", codigo);

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                type: "post",
                contentType: false,
                processData: false,
                data: fmProd,
                dataType: "json",
                beforeSend: function () {
                    __Progress("Buscando producto...");
                },
                success: function (response) {
                    console.log("Respuesta de búsqueda de producto:", response);

                    if (response.type === "success" && response.data && response.data.length > 0) {
                        let producto = response.data[0];
                        CargarProductoEnTabla(producto);

                        Swal.fire({
                            icon: 'success',
                            title: 'Producto encontrado',
                            text: 'El producto se ha cargado en la tabla.',
                            showConfirmButton: false,
                            timer: 1500
                        });
                    }
                    else {
                        Swal.fire({
                            icon: 'info',
                            title: 'Producto no encontrado',
                            text: response.text || 'No se encontró el producto con el código especificado.'
                        });
                    }
                },
                error: function (xhr, status, error) {
                    console.error("Error AJAX buscar producto:", error, xhr.responseText);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Ocurrió un error al buscar el producto. Por favor intente nuevamente.'
                    });
                },
                complete: function () {
                    __ProgressOff();
                }
            });
        }

        // Función para cargar un producto específico en la tabla
        function CargarProductoEnTabla(producto) {
            console.log("Cargando producto en tabla:", producto);

            // Limpiar tabla primero
            var table = $('#grvProductosCompra').DataTable();
            table.clear();

            // Crear la estructura correcta para el DataTable
            var newRow = {
                id_producto: producto.id_producto,
                codigo: producto.codigo,
                nombre: producto.nombre,
                stock_actual: producto.stock_actual || 0,
                costo_unitario: producto.costo_unitario || 0,
                precio_unitario: producto.precio_unitario || 0,
                min_descuento: producto.min_descuento || 0,
                categoria: producto.categoria || "",
                descripcion: producto.descripcion || "",
                img_producto: producto.img_producto || ""
            };

            // Agregar el producto encontrado
            table.row.add(newRow).draw();

            console.log("Producto cargado en tabla exitosamente");
        }

        // Función para limpiar la tabla de productos
        function LimpiarTablaProductos() {
            var table = $('#grvProductosCompra').DataTable();
            table.clear().draw();
            console.log("Tabla de productos limpiada");
        }

        // Función para guardar la compra
        function GuardarCompra() {
            console.log("=== INICIANDO GUARDADO DE COMPRA ===");

            // Validaciones básicas
            if (productosCompra.length === 0) {
                Swal.fire('Error', 'Debe agregar al menos un producto a la compra.', 'error');
                return;
            }

            var codigoCompra = $('#codigoCompra').val().trim();
            if (!codigoCompra) {
                Swal.fire('Error', 'Debe ingresar el código de compra.', 'error');
                return;
            }

            var fechaCompra = $('#fechaCompra').val();
            if (!fechaCompra) {
                Swal.fire('Error', 'Debe seleccionar la fecha de compra.', 'error');
                return;
            }

            var formaPago = $('#ddlFormaPago').val();
            if (!formaPago) {
                Swal.fire('Error', 'Debe seleccionar una forma de pago.', 'error');
                return;
            }

            // Calcular totales
            var subtotal = 0;
            productosCompra.forEach(function(producto) {
                subtotal += producto.cantidad * producto.costo_unitario;
            });

            // Crear objeto de compra con la estructura que me especificaste
            var compraData = {
                id_contribuyente: null, // Se asignará en el servidor desde la sesión
                id_sucursal: null, // Se asignará en el servidor desde la sesión
                id_usuario: null, // Se asignará en el servidor desde la sesión
                moneda: "GTQ",
                mto_subtotal: subtotal,
                mto_recargo: 0,
                mto_descuento: 0,
                mto_total: subtotal,
                id_moneda: 1, // GTQ por defecto
                productos: productosCompra,
                medios_pago: [
                    {
                        codigo: formaPago,
                        descripcion: $('#ddlFormaPago option:selected').text(),
                        moneda: "GTQ",
                        id_medio_pago: parseInt(formaPago) || 0,
                        cod_entidad: "",
                        cod_referencia: "",
                        id_banco: 0,
                        fecha_referencia: new Date().toISOString(),
                        num_referencia: "",
                        num_autorizacion: "",
                        monto: subtotal
                    }
                ],
                encabezado: {
                    id_referencia: codigoCompra,
                    tipo_transaccion: "COMPRA",
                    fecha: fechaCompra,
                    id_caja: null, // Se asignará en el servidor o null
                    id_cliente: "" // En compras sería id_proveedor, pero adaptamos la estructura
                }
            };

            console.log("Datos de compra a enviar:", compraData);

            // Enviar al servidor
            EnviarCompraAlServidor(compraData);
        }

        // Función para enviar la compra al servidor
        function EnviarCompraAlServidor(compraData) {
            console.log("=== ENVIANDO COMPRA AL SERVIDOR ===");

            var frmCompra = new FormData();
            frmCompra.append("mth", mtdEnc("save/purchase"));
            frmCompra.append("data", mtdEnc(JSON.stringify(compraData)));

            $.ajax({
                url: `${_url_redirect}Controllers/compra.ashx?mtd=${mtdEnc("save/purchase")}`,
                data: frmCompra,
                type: "POST",
                contentType: false,
                processData: false,
                ContentType: "text/html;charset=utf-8",
                dataType: "json",
                beforeSend: function () {
                    __Progress("Guardando compra...");
                },
                success: function (response) {
                    __ProgressOff();
                    console.log("Respuesta del servidor:", response);

                    if (response.type === "success") {
                        Swal.fire({
                            title: 'Éxito',
                            text: 'La compra se guardó correctamente.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Limpiar formulario
                            LimpiarFormulario();
                        });
                    } else {
                        Swal.fire('Error', response.text || 'Error al guardar la compra.', 'error');
                    }
                },
                error: function (xhr, status, error) {
                    __ProgressOff();
                    console.error('Error AJAX:', error);
                    console.error('Response:', xhr.responseText);
                    Swal.fire('Error', 'Ocurrió un error al comunicarse con el servidor.', 'error');
                },
                complete: function () {
                    __ProgressOff();
                }
            });
        }

        // Función para limpiar el formulario después de guardar
        function LimpiarFormulario() {
            productosCompra = [];
            $('#codigoCompra').val('');
            $('#polizaDuca').val('');
            $('#fechaCompra').val('');
            $('#ddlFormaPago').val('');

            // Limpiar inputs de la tabla
            $('#grvProductosCompra tbody tr').each(function() {
                $(this).find('.cantidad-input').val('');
                $(this).find('.costo-input').val('');
                $(this).find('.precio-input').val('');
                $(this).find('.precio-min-input').val('');
                $(this).find('.costo-total').text('0.00');
            });

            console.log("Formulario limpiado");
        }



        // Función para mostrar productos agregados a la compra
        function MostrarProductosAgregados() {
            if (productosCompra.length === 0) {
                Swal.fire('Información', 'No hay productos agregados a la compra.', 'info');
                return;
            }

            var html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>Código</th><th>Producto</th><th>Cantidad</th><th>Costo U.</th><th>Total</th><th>Acción</th></tr></thead>';
            html += '<tbody>';

            productosCompra.forEach(function(producto, index) {
                var total = producto.cantidad * producto.costo_unitario;
                html += `<tr>
                    <td>${producto.codigo}</td>
                    <td>${producto.nombre}</td>
                    <td>${producto.cantidad}</td>
                    <td>Q ${producto.costo_unitario.toFixed(2)}</td>
                    <td>Q ${total.toFixed(2)}</td>
                    <td><button type="button" class="btn btn-sm btn-danger" onclick="EliminarProductoCompra(${index})">
                        <i class="fa fa-trash"></i>
                    </button></td>
                </tr>`;
            });

            html += '</tbody></table></div>';

            Swal.fire({
                title: 'Productos en la Compra',
                html: html,
                width: '80%',
                showCloseButton: true,
                showConfirmButton: false
            });
        }

        // Función para eliminar producto de la compra
        function EliminarProductoCompra(index) {
            if (index >= 0 && index < productosCompra.length) {
                var producto = productosCompra[index];

                Swal.fire({
                    title: '¿Eliminar producto?',
                    text: `¿Está seguro de eliminar "${producto.nombre}" de la compra?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Sí, eliminar',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.isConfirmed) {
                        productosCompra.splice(index, 1);
                        ActualizarResumenCompra();
                        Swal.fire('Eliminado', 'El producto fue eliminado de la compra.', 'success');
                    }
                });
            }
        }
    </script>

</asp:Content>

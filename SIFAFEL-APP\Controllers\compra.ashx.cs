﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SIFAFEL_CORE.api_request;
using SIFAFEL_CORE.api_response;
using SIFAFEL_CORE.core;
using SIFAFEL_CORE.validation;
using SIFAFEL_CORE.web_request;
using SIFAFEL_CORE.web_response;
using SIFAFEL_MODEL.Data_Model_API.Autenticacion;
using SIFAFEL_MODEL.Data_Model_API.Modulos;
using SIFAFEL_MODEL.Data_Model_API.Token;
using SIFAFEL_MODEL.DataTransferObject;
using SIFAFEL_MODEL.Utils;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services.Description;
using static SIFAFEL_CORE.Core.Utility;

namespace SIFAFEL_APP.Controllers
{
    /// <summary>
    /// Handler de compra
    /// </summary>
    public class compra : SessionHandler
    {
        public InfoUsuario info_usuario = new InfoUsuario();

        public override void HandleRequest(HttpContext context)
        {
            info_usuario = context.Session["AuthInfoUser"] as InfoUsuario;
            string metodo = RequestParameters.GetValue("mth");

            switch (metodo)
            {
                case "get/products":
                    context.Response.Write(JsonConvert.SerializeObject(GetProducts()));
                    break;
                case "get/product/by/id":
                    context.Response.Write(JsonConvert.SerializeObject(GetProductByCode()));
                    break;
                case "get/providers":
                    context.Response.Write(JsonConvert.SerializeObject(GetProviders()));
                    break;
                case "get/provider/by/nit":
                    string nit = context.Request["nit"];
                    context.Response.Write(JsonConvert.SerializeObject(GetProviderByNIT(nit)));
                    break;
                case "save/purchase":
                    context.Response.Write(JsonConvert.SerializeObject(SavePurchase()));
                    break;
                case "get/purchases":
                    context.Response.Write(JsonConvert.SerializeObject(GetPurchases()));
                    break;
                case "get/rpt":
                    context.Response.Write(JsonConvert.SerializeObject(GetReportDocument()));
                    break;
                case "create/provider":
                    string nitProveedor = context.Request["nit"];
                    string nombre = context.Request["nombre"];
                    string direccion = context.Request["direccion"];
                    string telefono = context.Request["telefono"];
                    string email = context.Request["email"];
                    context.Response.Write(JsonConvert.SerializeObject(CreateProvider(nitProveedor, nombre, direccion, telefono,email)));
                    break;
                default:
                    if (context.Request["id"] != null)
                    {
                        context.Response.Write(JsonConvert.SerializeObject(GetInfoPurchase()));
                    }
                    break;
            }
        }

        /// <summary>
        /// Recupera los productos
        /// </summary>
        /// <returns></returns>
        public response GetProducts()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                List<VWInventarioContribuyenteDTO> productos = new List<VWInventarioContribuyenteDTO>();

                try
                {
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    rest.Add("pSoloExistencia", "S");
                    productos = rest.GenericRestClient<List<VWInventarioContribuyenteDTO>, DBNull>("api_inventory_get_products", token.access_token, TypeMethod.Get);

                    if (productos != null && productos.Count > 0)
                    {
                        response.data = productos;
                        response.text = "La búsqueda se realizó correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = "No se encontraron productos.";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.title = "Error";
                    response.text = "Ocurrió un error al consultar los productos.";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            else
            {
                response.title = "Sesión inválida";
                response.text = "La sesión ha expirado o no es válida.";
                response.type = TIPO_MENSAJE.ERROR;
            }

            return response;
        }



        /// <summary>
        /// Recupera un producto por el codigo
        /// </summary>
        /// <returns></returns>
        public response GetProductByCode()
        {
            response message = new response();
            string PRODUCT_CODE = RequestParameters.GetValue("productCode");

            if (!string.IsNullOrWhiteSpace(PRODUCT_CODE))
            {
                SessionManager sessionManager = new SessionManager();
                TokenManager tokenManager = new TokenManager();

                if (sessionManager.Valid() && tokenManager.Valid())
                {
                    try
                    {
                        token_response token = tokenManager.Get();
                        List<VWInventarioContribuyenteDTO> productos = new List<VWInventarioContribuyenteDTO>();

                        rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);

                        rest.Add("pIdContribuyente", sessionManager.GetIdContribuyente().ToString());
                        rest.Add("pIdSucursal", sessionManager.GetIdSucursal().ToString());
                        rest.Add("pCodProduct", PRODUCT_CODE);

                        productos = rest.GenericRestClient<List<VWInventarioContribuyenteDTO>, DBNull>("api_mod_get_producto", token.access_token, TypeMethod.Get);
                        message.data = productos;

                        if (productos.Count > 0)
                        {
                            message.title = "";
                            message.text = "La búsqueda se realizó correctamente.";
                            message.type = TIPO_MENSAJE.SUCCESS;
                        }
                        else
                        {
                            message.title = "Código no registrado";
                            message.text = "Comuníquese con su encargado para el registro del producto";
                            message.type = TIPO_MENSAJE.WARNING;
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                }
            }
            return message;
        }

        /// <summary>
        /// Recupera los proveedores
        /// </summary>
        /// <returns></returns>
        public response GetProviders()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                IEnumerable<ModProveedorDTO> proveedores = null;

                try
                {
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    proveedores = rest.GenericRestClient<IEnumerable<ModProveedorDTO>, DBNull>("api_mod_proveedor_get_list", token.access_token, TypeMethod.Get);

                    if (proveedores != null && proveedores.Count() > 0)
                    {
                        response.data = proveedores;
                        response.title = "Búsqueda exitosa";
                        response.text = "La búsqueda se realizó correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.title = "Sin resultados";
                        response.text = "No se encontraron proveedores.";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.title = "Error";
                    response.text = "Ocurrió un error al consultar los proveedores.";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            return response;
        }

        /// <summary>
        /// Recupera el proveedor segun el NIT
        /// </summary>
        /// <returns></returns>
        public response GetProviderByNIT(string nit)
        {
            response message = new response();
            //string NIT_PROVEEDOR = RequestParameters.GetValue("nit");
            string nitDescencriptado = encript_core.GetValue(nit);

            if (!string.IsNullOrWhiteSpace(nitDescencriptado))
            {
                SessionManager sessionManager = new SessionManager();
                TokenManager tokenManager = new TokenManager();

                if (sessionManager.Valid() && tokenManager.Valid())
                {
                    try
                    {
                        token_response token = tokenManager.Get();
                        ModProveedorDTO proveedor = new ModProveedorDTO();

                        rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                        rest.Add("nit", nitDescencriptado);

                        proveedor = rest.GenericRestClient<ModProveedorDTO, DBNull>("api_mod_proveedor_getByNIT", token.access_token, TypeMethod.Get);
                        message.data = proveedor;

                        if (proveedor?.id_proveedor > 0)
                        {
                            message.title = "";
                            message.text = "La búsqueda se realizó correctamente.";
                            message.type = TIPO_MENSAJE.SUCCESS;
                        }
                        else
                        {
                            message.title = "NIT no registrado";
                            message.text = "El NIT ingresado no está registrado en el sistema.";
                            message.type = TIPO_MENSAJE.WARNING;
                        }
                    }
                    catch (Exception ex)
                    {
                        message.title = "Error";
                        message.text = "Ocurrió un error al consultar el proveedor.";
                        message.type = TIPO_MENSAJE.ERROR;
                    }
                }
            }
            return message;
        }

        /// <summary>
        /// Proceso principal para guardar una compra
        /// </summary>
        /// <returns></returns>
        public response SavePurchase()
        {
            response message = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();

                try
                {
                    // Recibir los datos con la estructura extendida
                    ModCompraExtendidaDTO datosCompra = RequestParameters.GetObject<ModCompraExtendidaDTO>("data");

                    // Obtener datos de la sesión
                    var infoUsuario = sessionManager.Get();
                    datosCompra.id_contribuyente = sessionManager.GetIdContribuyente();
                    datosCompra.id_sucursal = sessionManager.GetIdSucursal();
                    datosCompra.id_usuario = infoUsuario?.id;

                    // Serializar el JSON que se va a enviar al endpoint
                    string jsonParaEnviar = JsonConvert.SerializeObject(datosCompra, Formatting.Indented);

                    // Log para debugging
                    System.Diagnostics.Debug.WriteLine($"=== JSON QUE SE ENVIARÁ AL ENDPOINT ===");
                    System.Diagnostics.Debug.WriteLine(jsonParaEnviar);
                    System.Diagnostics.Debug.WriteLine($"=== FIN JSON ===");
                    System.Diagnostics.Debug.WriteLine($"- ID Contribuyente: {datosCompra.id_contribuyente}");
                    System.Diagnostics.Debug.WriteLine($"- ID Sucursal: {datosCompra.id_sucursal}");
                    System.Diagnostics.Debug.WriteLine($"- ID Usuario: {datosCompra.id_usuario}");
                    System.Diagnostics.Debug.WriteLine($"- Productos: {datosCompra.productos?.Count}");
                    System.Diagnostics.Debug.WriteLine($"- Total: {datosCompra.mto_total}");

                    // Hacer la consulta al API usando el endpoint configurado en Web.config
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    message = rest.GenericRestClient<response, ModCompraExtendidaDTO>("api_mod_guardar_compra", token.access_token, TypeMethod.Post, datosCompra);

                    if (message.type == TIPO_MENSAJE.SUCCESS)
                    {
                        message.title = "Operación completada";
                        message.text = "La compra se guardó exitosamente.";
                    }
                }
                catch (Exception ex)
                {
                    message.title = "Error";
                    message.text = "Ocurrió un error al guardar la compra.";
                    message.type = TIPO_MENSAJE.ERROR;
                }
            }
            return message;
        }

        /// <summary>
        /// Proceso principal para obtener las compras
        /// </summary>
        /// <returns></returns>
        public response GetPurchases()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                List<ModCompraDTO> purchases = new List<ModCompraDTO>();

                try
                {
                    // Aquí se implementaría la lógica para obtener las compras
                    // Similar a GetSales en venta.ashx.cs
                    // Por ahora, devolvemos una respuesta vacía
                    response.data = purchases;
                    response.text = "La búsqueda se realizó correctamente.";
                    response.type = TIPO_MENSAJE.SUCCESS;
                }
                catch (Exception ex)
                {
                    response.title = "Error";
                    response.text = "Ocurrió un error al consultar las compras.";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            return response;
        }

        /// <summary>
        /// Obtiene la información de una compra
        /// </summary>
        /// <returns></returns>
        public response GetInfoPurchase()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();

                string KEY_COMPRA = encript_core.GetSimple_Context("id");
                string ID_COMPRA = SecretKeyManager.Decrypt(KEY_COMPRA);

                try
                {
                    // Aquí se implementaría la lógica para obtener la información de una compra
                    // Similar a GetInfoSale en venta.ashx.cs
                    // Por ahora, devolvemos una respuesta vacía
                    response.type = TIPO_MENSAJE.SUCCESS;
                }
                catch (Exception ex)
                {
                    response.title = "Error";
                    response.text = "Ocurrió un error al consultar la información de la compra.";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            return response;
        }

        /// <summary>
        /// Obtiene el documento de reporte de una compra
        /// </summary>
        /// <returns></returns>
        public response_file GetReportDocument()
        {
            response_file response = new response_file();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();

                string KEY_COMPRA = encript_core.GetSimple_Context("id");
                string ID_COMPRA = SecretKeyManager.Decrypt(KEY_COMPRA);

                try
                {
                    // Aquí se implementaría la lógica para obtener el reporte de una compra
                    // Similar a GetReportDocument en venta.ashx.cs
                    // Por ahora, devolvemos una respuesta vacía
                }
                catch (Exception ex)
                {
                    //response.title = "Error";
                    //response.text = "Ocurrió un error al generar el reporte.";
                    //response.type = TIPO_MENSAJE.ERROR;
                }
            }
            return response;
        }

        /// <summary>
        /// Crear un nuevo proveedor
        /// </summary>
        /// <returns></returns>
        public response CreateProvider(string nit, string nombre, string direccion, string telefono,string email)
        {
            response message = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            // Desencriptar los datos del proveedor
            string nitDesencriptado = encript_core.GetValue(nit);
            string nombreDesencriptado = encript_core.GetValue(nombre);
            string direccionDesencriptada = encript_core.GetValue(direccion);
            string telefonoDesencriptado = encript_core.GetValue(telefono);
            string emailDesencriptado = encript_core.GetValue(email);

            if (!string.IsNullOrWhiteSpace(nitDesencriptado) && !string.IsNullOrWhiteSpace(nombreDesencriptado))
            {
                if (sessionManager.Valid() && tokenManager.Valid())
                {
                    try
                    {
                        token_response token = tokenManager.Get();

                        // Crear el objeto ModProveedorDTO
                        ModProveedorDTO nuevoProveedor = new ModProveedorDTO
                        {
                            nit = nitDesencriptado,
                            nombres = nombreDesencriptado,
                            //apellidos = "", 
                            telefono = telefonoDesencriptado,
                            email = emailDesencriptado,
                            direccion = direccionDesencriptada,
                            estado = true,
                            fecha_regi = DateTime.Now
                            //id_usu_regi = sessionManager.GetIdUsuario() ?? 0
                        };

                        // Llamar a la API para crear el proveedor
                        rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                        message = rest.GenericRestClient<response, ModProveedorDTO>("api_mod_proveedor_crear", token.access_token, TypeMethod.Post, nuevoProveedor);

                        if (message.type == TIPO_MENSAJE.SUCCESS)
                        {
                            message.title = "Proveedor creado";
                            message.text = "El proveedor se creó correctamente.";
                        }
                    }
                    catch (Exception ex)
                    {
                        message.title = "Error";
                        message.text = "Ocurrió un error al crear el proveedor.";
                        message.type = TIPO_MENSAJE.ERROR;
                    }
                }
                else
                {
                    message.title = "Sesión inválida";
                    message.text = "La sesión ha expirado o no es válida.";
                    message.type = TIPO_MENSAJE.ERROR;
                }
            }
            else
            {
                message.title = "Datos incompletos";
                message.text = "El NIT y nombre del proveedor son requeridos.";
                message.type = TIPO_MENSAJE.ERROR;
            }

            return message;
        }
    }
}
